import React, { RefObject } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Send } from 'lucide-react';

interface ChatMessage {
  id: string;
  userId: string;
  userName: string;
  userAvatar: string;
  message: string;
  timestamp: Date;
}

interface ChatInterfaceProps {
  messages: ChatMessage[];
  newMessage: string;
  onNewMessageChange: (value: string) => void;
  onSendMessage: () => void;
  chatContainerRef: RefObject<HTMLDivElement>;
}

const MessageSquare = ({ className }: { className?: string }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
  </svg>
);

const ChatInterface: React.FC<ChatInterfaceProps> = ({
  messages,
  newMessage,
  onNewMessageChange,
  onSendMessage,
  chatContainerRef
}) => {
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      onSendMessage();
    }
  };

  const formatMessageTime = (timestamp: Date) => {
    return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div className="flex-1 flex flex-col">
      <div className="p-4 border-b dark:border-gray-700">
        <h3 className="font-semibold flex items-center dark:text-white">
          <MessageSquare className="w-4 h-4 mr-2" />
          Chat ({messages.length})
        </h3>
      </div>
      
      {/* Messages */}
      <div 
        ref={chatContainerRef}
        className="flex-1 p-4 space-y-3 overflow-y-auto max-h-64"
        role="log"
        aria-label="Chat messages"
      >
        {messages.length === 0 ? (
          <div className="text-center text-gray-500 dark:text-gray-400 py-8">
            <MessageSquare className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p>No messages yet. Start the conversation!</p>
          </div>
        ) : (
          messages.map((message) => (
            <div key={message.id} className="flex space-x-2">
              <Avatar className="w-8 h-8 flex-shrink-0">
                <AvatarImage src={message.userAvatar} alt={message.userName} />
                <AvatarFallback className="text-xs">
                  {message.userName.charAt(0)}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <div className="flex items-baseline space-x-2">
                  <p className="font-medium text-sm dark:text-gray-200">
                    {message.userName}
                  </p>
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {formatMessageTime(message.timestamp)}
                  </span>
                </div>
                <p className="text-sm text-gray-700 dark:text-gray-300 break-words">
                  {message.message}
                </p>
              </div>
            </div>
          ))
        )}
      </div>
      
      {/* Message Input */}
      <div className="p-4 border-t dark:border-gray-700">
        <div className="flex space-x-2">
          <Input
            value={newMessage}
            onChange={(e) => onNewMessageChange(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Type a message..."
            className="flex-1 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            maxLength={500}
            aria-label="Type your message"
          />
          <Button 
            onClick={onSendMessage}
            disabled={!newMessage.trim()}
            size="sm"
            className="px-3"
            aria-label="Send message"
          >
            <Send className="w-4 h-4" />
          </Button>
        </div>
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
          Press Enter to send • {newMessage.length}/500
        </p>
      </div>
    </div>
  );
};

export default ChatInterface;