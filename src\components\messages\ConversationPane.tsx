import React from 'react';
import { MessageCircle } from 'lucide-react';
import MessageBubble from '../MessageBubble';
import MessageComposer from '../MessageComposer';
import ConversationHeader from './ConversationHeader';
import { MOCK_IMAGES } from '@/lib/constants';
import { toast } from 'sonner';

import { Message, Conversation } from '@/types/messaging';

interface ConversationPaneProps {
  selectedConversation: Conversation | null;
  messages: Message[];
  showConversation: boolean;
  isMobile: boolean;
  onBackToList: () => void;
  onSendMessage: (content: string) => void;
}

const ConversationPane: React.FC<ConversationPaneProps> = ({
  selectedConversation,
  messages,
  showConversation,
  isMobile,
  onBackToList,
  onSendMessage
}) => {
  if (!showConversation || !selectedConversation) {
    // Empty state for desktop when no conversation is selected
    if (!isMobile) {
      return (
        <div className="flex-1 flex items-center justify-center bg-gray-50 dark:bg-gray-900">
          <div className="text-center p-5">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4 dark:bg-blue-900/30">
              <MessageCircle className="w-8 h-8 text-blue-600 dark:text-blue-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2 dark:text-white">Your Messages</h3>
            <p className="text-gray-500 mb-4 dark:text-gray-400">Select a conversation to start chatting</p>
          </div>
        </div>
      );
    }
    return null;
  }

  return (
    <div className="flex-1 flex flex-col h-full overflow-hidden">
      {/* Conversation Header */}
      <ConversationHeader
        user={selectedConversation.user}
        isMobile={isMobile}
        onBackToList={onBackToList}
      />
      
      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-3 space-y-4 bg-gray-50 dark:bg-gray-900">
        {messages.map((message) => (
          <div key={message.id}>
            <MessageBubble
              message={{
                ...message,
                conversationId: selectedConversation.id,
                type: 'text',
                reactions: [],
              }}
              sender={{
                id: message.senderId,
                name: message.senderId === 'currentUser' ? 
                  'You' : 
                  selectedConversation.user.name,
                avatar: message.senderId === 'currentUser' ? 
                  MOCK_IMAGES.AVATARS[7] : 
                  selectedConversation.user.avatar,
                isOnline: message.senderId === 'currentUser' ? 
                  true : 
                  selectedConversation.user.isOnline,
                lastSeen: message.senderId === 'currentUser' ? 
                  undefined : 
                  selectedConversation.user.lastActive
              }}
              isOwn={message.senderId === 'currentUser'}
              showAvatar={true}
              showTimestamp={true}
              onReply={(_message) => toast.info('Reply feature coming soon')}
              onEdit={(_message) => toast.info('Edit feature coming soon')}
              onDelete={(_message) => toast.info('Delete feature coming soon')}
            />
          </div>
        ))}
      </div>
      
      {/* Message Input */}
      <MessageComposer
        conversationId={selectedConversation.id}
        onSendMessage={onSendMessage}
        placeholder="Type a message..."
      />
    </div>
  );
};

export default ConversationPane;
