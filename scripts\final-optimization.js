#!/usr/bin/env node

/**
 * Final Optimization Script
 * 
 * This script performs final cleanup and optimization tasks:
 * - Removes unused files and dependencies
 * - Optimizes imports and exports
 * - Generates performance reports
 * - Updates documentation
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class FinalOptimizer {
  constructor() {
    this.projectRoot = process.cwd();
    this.srcDir = path.join(this.projectRoot, 'src');
    this.optimizationReport = {
      filesRemoved: [],
      importsOptimized: [],
      bundleSize: {},
      performanceMetrics: {},
      recommendations: []
    };
  }

  async run() {
    console.log('🚀 Starting final optimization...\n');

    try {
      await this.analyzeProject();
      await this.removeUnusedFiles();
      await this.optimizeImports();
      await this.generateBundleReport();
      await this.updatePackageJson();
      await this.generateOptimizationReport();
      
      console.log('✅ Final optimization completed successfully!\n');
      this.printSummary();
    } catch (error) {
      console.error('❌ Optimization failed:', error.message);
      process.exit(1);
    }
  }

  async analyzeProject() {
    console.log('📊 Analyzing project structure...');
    
    const stats = this.getProjectStats();
    console.log(`   - Total files: ${stats.totalFiles}`);
    console.log(`   - TypeScript files: ${stats.tsFiles}`);
    console.log(`   - Component files: ${stats.componentFiles}`);
    console.log(`   - Test files: ${stats.testFiles}\n`);
  }

  getProjectStats() {
    const getAllFiles = (dir, fileList = []) => {
      const files = fs.readdirSync(dir);
      
      files.forEach(file => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
          getAllFiles(filePath, fileList);
        } else if (stat.isFile()) {
          fileList.push(filePath);
        }
      });
      
      return fileList;
    };

    const allFiles = getAllFiles(this.srcDir);
    
    return {
      totalFiles: allFiles.length,
      tsFiles: allFiles.filter(f => f.endsWith('.ts') || f.endsWith('.tsx')).length,
      componentFiles: allFiles.filter(f => f.includes('/components/') && f.endsWith('.tsx')).length,
      testFiles: allFiles.filter(f => f.includes('test') || f.includes('spec')).length
    };
  }

  async removeUnusedFiles() {
    console.log('🧹 Removing unused files...');
    
    const unusedFiles = [
      // Remove any duplicate or legacy files
      'src/components/AdvancedSearch.tsx.bak',
      'src/components/PostCard.old.tsx',
      'src/components/Search.legacy.tsx',
      // Remove any temporary files
      'src/**/*.tmp',
      'src/**/*.backup',
      // Remove any empty directories
    ];

    let removedCount = 0;
    
    unusedFiles.forEach(pattern => {
      try {
        const files = this.globFiles(pattern);
        files.forEach(file => {
          if (fs.existsSync(file)) {
            fs.unlinkSync(file);
            this.optimizationReport.filesRemoved.push(file);
            removedCount++;
          }
        });
      } catch (error) {
        // File doesn't exist or pattern doesn't match
      }
    });

    console.log(`   - Removed ${removedCount} unused files\n`);
  }

  globFiles(pattern) {
    // Simple glob implementation for basic patterns
    if (pattern.includes('**/*')) {
      const baseDir = pattern.split('**/*')[0];
      const extension = pattern.split('**/*')[1];
      
      if (fs.existsSync(baseDir)) {
        return this.findFilesRecursive(baseDir, extension);
      }
    }
    
    return fs.existsSync(pattern) ? [pattern] : [];
  }

  findFilesRecursive(dir, extension) {
    const files = [];
    
    if (!fs.existsSync(dir)) return files;
    
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        files.push(...this.findFilesRecursive(fullPath, extension));
      } else if (item.endsWith(extension)) {
        files.push(fullPath);
      }
    });
    
    return files;
  }

  async optimizeImports() {
    console.log('📦 Optimizing imports...');
    
    const tsFiles = this.findFilesRecursive(this.srcDir, '.tsx')
      .concat(this.findFilesRecursive(this.srcDir, '.ts'));
    
    let optimizedCount = 0;
    
    tsFiles.forEach(file => {
      try {
        const content = fs.readFileSync(file, 'utf8');
        const optimizedContent = this.optimizeFileImports(content);
        
        if (content !== optimizedContent) {
          fs.writeFileSync(file, optimizedContent);
          this.optimizationReport.importsOptimized.push(file);
          optimizedCount++;
        }
      } catch (error) {
        console.warn(`   - Warning: Could not optimize ${file}: ${error.message}`);
      }
    });

    console.log(`   - Optimized imports in ${optimizedCount} files\n`);
  }

  optimizeFileImports(content) {
    let optimized = content;
    
    // Remove unused React import when only using types
    optimized = optimized.replace(
      /import React,\s*{\s*([^}]*)\s*}\s*from\s*['"]react['"];/g,
      (match, imports) => {
        if (!content.includes('React.') && !content.includes('<React.')) {
          return `import { ${imports} } from 'react';`;
        }
        return match;
      }
    );
    
    // Sort imports alphabetically
    const importRegex = /^import\s+.*from\s+['"][^'"]+['"];?\s*$/gm;
    const imports = content.match(importRegex) || [];
    
    if (imports.length > 1) {
      const sortedImports = imports.sort((a, b) => {
        // React imports first
        if (a.includes("'react'") && !b.includes("'react'")) return -1;
        if (!a.includes("'react'") && b.includes("'react'")) return 1;
        
        // External libraries next
        const aIsExternal = !a.includes('./') && !a.includes('../') && !a.includes('@/');
        const bIsExternal = !b.includes('./') && !b.includes('../') && !b.includes('@/');
        
        if (aIsExternal && !bIsExternal) return -1;
        if (!aIsExternal && bIsExternal) return 1;
        
        // Alphabetical order
        return a.localeCompare(b);
      });
      
      // Replace imports section with sorted version
      const firstImport = imports[0];
      const lastImport = imports[imports.length - 1];
      const startIndex = content.indexOf(firstImport);
      const endIndex = content.indexOf(lastImport) + lastImport.length;
      
      optimized = content.substring(0, startIndex) + 
                 sortedImports.join('\n') + '\n' +
                 content.substring(endIndex);
    }
    
    return optimized;
  }

  async generateBundleReport() {
    console.log('📈 Generating bundle analysis...');
    
    try {
      // Run build to get bundle stats
      console.log('   - Building project for analysis...');
      execSync('npm run build', { stdio: 'pipe' });
      
      // Analyze bundle if analyzer is available
      try {
        execSync('npx vite-bundle-analyzer dist --mode static --report bundle-report.html', { stdio: 'pipe' });
        console.log('   - Bundle report generated: bundle-report.html');
      } catch (error) {
        console.log('   - Bundle analyzer not available, skipping detailed analysis');
      }
      
      // Get basic bundle info
      const distDir = path.join(this.projectRoot, 'dist');
      if (fs.existsSync(distDir)) {
        const bundleStats = this.analyzeBundleSize(distDir);
        this.optimizationReport.bundleSize = bundleStats;
        
        console.log(`   - Total bundle size: ${this.formatBytes(bundleStats.total)}`);
        console.log(`   - JavaScript size: ${this.formatBytes(bundleStats.js)}`);
        console.log(`   - CSS size: ${this.formatBytes(bundleStats.css)}`);
      }
      
    } catch (error) {
      console.warn('   - Warning: Could not generate bundle report:', error.message);
    }
    
    console.log('');
  }

  analyzeBundleSize(distDir) {
    const stats = { total: 0, js: 0, css: 0, assets: 0 };
    
    const analyzeDir = (dir) => {
      const items = fs.readdirSync(dir);
      
      items.forEach(item => {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          analyzeDir(fullPath);
        } else {
          const size = stat.size;
          stats.total += size;
          
          if (item.endsWith('.js') || item.endsWith('.mjs')) {
            stats.js += size;
          } else if (item.endsWith('.css')) {
            stats.css += size;
          } else {
            stats.assets += size;
          }
        }
      });
    };
    
    analyzeDir(distDir);
    return stats;
  }

  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  async updatePackageJson() {
    console.log('📝 Updating package.json...');
    
    const packageJsonPath = path.join(this.projectRoot, 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    // Add optimization scripts if they don't exist
    if (!packageJson.scripts['analyze']) {
      packageJson.scripts['analyze'] = 'vite-bundle-analyzer dist';
    }
    
    if (!packageJson.scripts['optimize']) {
      packageJson.scripts['optimize'] = 'node scripts/final-optimization.js';
    }
    
    // Update version if this is a significant update
    const currentVersion = packageJson.version || '1.0.0';
    const versionParts = currentVersion.split('.');
    versionParts[1] = (parseInt(versionParts[1]) + 1).toString();
    packageJson.version = versionParts.join('.');
    
    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
    console.log(`   - Updated version to ${packageJson.version}\n`);
  }

  async generateOptimizationReport() {
    console.log('📋 Generating optimization report...');
    
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        filesRemoved: this.optimizationReport.filesRemoved.length,
        importsOptimized: this.optimizationReport.importsOptimized.length,
        bundleSize: this.optimizationReport.bundleSize
      },
      details: this.optimizationReport,
      recommendations: [
        'Consider implementing service worker for better caching',
        'Monitor Core Web Vitals in production',
        'Set up performance budgets in CI/CD',
        'Implement progressive loading for images',
        'Consider using React 18 concurrent features'
      ]
    };
    
    const reportPath = path.join(this.projectRoot, 'optimization-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log(`   - Report saved to: optimization-report.json\n`);
  }

  printSummary() {
    console.log('📊 Optimization Summary:');
    console.log('========================');
    console.log(`✅ Files removed: ${this.optimizationReport.filesRemoved.length}`);
    console.log(`✅ Imports optimized: ${this.optimizationReport.importsOptimized.length}`);
    
    if (this.optimizationReport.bundleSize.total) {
      console.log(`✅ Bundle size: ${this.formatBytes(this.optimizationReport.bundleSize.total)}`);
    }
    
    console.log('\n🎯 Next Steps:');
    console.log('- Run tests to ensure everything works: npm test');
    console.log('- Check bundle analysis: open bundle-report.html');
    console.log('- Review optimization report: optimization-report.json');
    console.log('- Deploy optimized version to production');
    console.log('\n🚀 Your Facebook clone is now optimized and ready!');
  }
}

// Run the optimizer
if (require.main === module) {
  const optimizer = new FinalOptimizer();
  optimizer.run().catch(console.error);
}

module.exports = FinalOptimizer;
