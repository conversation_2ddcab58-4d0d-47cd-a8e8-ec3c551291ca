import React, { useState, useRef } from 'react';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Film, Users } from 'lucide-react';
import { MOCK_IMAGES, getSafeImage } from '@/lib/constants';
import { useVideoPlayer } from '@/hooks/useVideoPlayer';
import { useWatchTogetherChat } from '@/hooks/useWatchTogetherChat';
import { useControlsVisibility } from '@/hooks/useControlsVisibility';
import WatchTogetherVideoPlayer from '@/components/WatchTogetherVideoPlayer';
import ParticipantsList from '@/components/ParticipantsList';
import ChatInterface from '@/components/ChatInterface';
import InviteFriendsModal from '@/components/InviteFriendsModal';

interface WatchTogetherProps {
  videoId?: string;
  roomId?: string;
  isOpen?: boolean;
  onClose?: () => void;
}

interface Participant {
  id: string;
  name: string;
  avatar: string;
  isHost: boolean;
  isMuted: boolean;
  isVideoOn: boolean;
  isSpeaking?: boolean;
}


const WatchTogether: React.FC<WatchTogetherProps> = ({
  videoId: _videoId = '1',
  roomId,
  isOpen = false,
  onClose
}) => {
  const [participants, setParticipants] = useState<Participant[]>([
    {
      id: 'user-1',
      name: 'You (Host)',
      avatar: getSafeImage('AVATARS', 7),
      isHost: true,
      isMuted: false,
      isVideoOn: true,
      isSpeaking: false
    },
    {
      id: 'user-2',
      name: 'Sarah Johnson',
      avatar: MOCK_IMAGES.AVATARS[0],
      isHost: false,
      isMuted: true,
      isVideoOn: false,
      isSpeaking: false
    },
    {
      id: 'user-3',
      name: 'Mike Chen',
      avatar: MOCK_IMAGES.AVATARS[1],
      isHost: false,
      isMuted: false,
      isVideoOn: true,
      isSpeaking: true
    }
  ]);
  

  const [showInviteModal, setShowInviteModal] = useState(false);
  const [linkCopied, setLinkCopied] = useState(false);
  
  const videoRef = useRef<HTMLVideoElement>(null);
  const progressBarRef = useRef<HTMLDivElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);

  // Session data
  const sessionId = roomId || `watch-${Date.now().toString(36)}`;
  const inviteLink = `https://yourapp.com/watch/${sessionId}`;

  // Custom hooks
  const {
    isPlaying,
    isMuted,
    currentTime,
    duration,
    handlePlay,
    handleMute,
    handleProgressClick,
    formatTime
  } = useVideoPlayer(videoRef, progressBarRef);

  const {
    messages,
    newMessage,
    setNewMessage,
    handleSendMessage
  } = useWatchTogetherChat(chatContainerRef, setParticipants);

  const { showControls, handleMouseMove } = useControlsVisibility(isPlaying);

  // Handle copy invite link
  const handleCopyInvite = async () => {
    try {
      await navigator.clipboard.writeText(inviteLink);
      setLinkCopied(true);
      setTimeout(() => setLinkCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy link:', err);
    }
  };

  const handleInviteFriends = () => {
    setShowInviteModal(true);
  };

  // Mock friends data for invitation
  const mockFriends = [
    {
      id: '4',
      name: 'Emma Davis',
      avatar: getSafeImage('AVATARS', 3),
      isOnline: true
    },
    {
      id: '5',
      name: 'Alex Chen',
      avatar: getSafeImage('AVATARS', 4),
      isOnline: false
    },
    {
      id: '6',
      name: 'Jordan Smith',
      avatar: getSafeImage('AVATARS', 5),
      isOnline: true
    }
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden p-0">
        <DialogHeader className="p-4 border-b">
          <DialogTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Film className="w-5 h-5" />
              <span>Watch Together</span>
            </div>
            <div className="flex items-center space-x-2 text-sm">
              <Badge variant="outline" className="bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
                <Users className="w-3 h-3 mr-1" />
                {participants.length} Watching
              </Badge>
              <Badge variant="outline" className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">
                Room: {sessionId.slice(0, 8)}
              </Badge>
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 h-[75vh]">
          {/* Video Player */}
          <div className="lg:col-span-2">
            <WatchTogetherVideoPlayer
              videoRef={videoRef}
              progressBarRef={progressBarRef}
              isPlaying={isPlaying}
              isMuted={isMuted}
              currentTime={currentTime}
              duration={duration}
              showControls={showControls}
              onPlay={handlePlay}
              onMute={handleMute}
              onProgressClick={handleProgressClick}
              onMouseMove={handleMouseMove}
              onInviteFriends={handleInviteFriends}
              formatTime={formatTime}
            />
          </div>
          
          {/* Participants and Chat */}
          <div className="flex flex-col h-full bg-white dark:bg-gray-800">
            <ParticipantsList participants={participants} />
            
            <ChatInterface
              messages={messages}
              newMessage={newMessage}
              onNewMessageChange={setNewMessage}
              onSendMessage={handleSendMessage}
              chatContainerRef={chatContainerRef}
            />
          </div>
        </div>
      </DialogContent>

      {/* Invite Friends Modal */}
      <InviteFriendsModal
        isOpen={showInviteModal}
        onClose={() => setShowInviteModal(false)}
        sessionId={sessionId}
        inviteLink={inviteLink}
        copySuccess={linkCopied}
        onCopyInvite={handleCopyInvite}
        friends={mockFriends}
      />
    </Dialog>
  );
};



export default WatchTogether;