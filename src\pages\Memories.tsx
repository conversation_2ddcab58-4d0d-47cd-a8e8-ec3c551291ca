import React, { useState, useMemo } from 'react';
import { Calendar, Bookmark, Sparkles, Clock } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Ta<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { format } from 'date-fns';
import MemoryOnThisDay from '@/components/MemoryOnThisDay';
import MemoryTimeline from '@/components/MemoryTimeline';
import MemoryYearInReview from '@/components/MemoryYearInReview';
import MemoriesCollection from '@/components/MemoriesCollection';
import { MemoryDetail } from '@/components/memories';

interface Memory {
  id: string;
  type: 'photo' | 'post' | 'event' | 'friendship';
  title: string;
  date: Date;
  yearsAgo: number;
  images: string[];
  content?: string;
  location?: string;
  peopleTagged?: {
    id: string;
    name: string;
    avatar: string;
  }[];
  interactions: {
    likes: number;
    comments: number;
    shares: number;
  };
  isLiked?: boolean;
  comments?: {
    id: string;
    author: {
      name: string;
      avatar: string;
    };
    content: string;
    timestamp: string;
  }[];
}

const Memories = () => {
  // Implement cache for selected memory to improve performance
  const [selectedMemory, setSelectedMemory] = useState<Memory | null>(null);
  const [isMemoryDetailOpen, setIsMemoryDetailOpen] = useState(false);
  const [activeView, setActiveView] = useState<'onThisDay' | 'timeline' | 'yearInReview' | 'collections'>('onThisDay');
  // Use memoization for constant values
  const today = useMemo(() => new Date(), []);

  const handleMemorySelect = (memory: Memory) => {
    setSelectedMemory(memory);
    setIsMemoryDetailOpen(true);
  };

  const renderMemoryContent = () => {
    switch (activeView) {
      case 'onThisDay':
        return <MemoryOnThisDay onMemorySelect={handleMemorySelect} />;
      case 'timeline':
        return <MemoryTimeline onMemorySelect={handleMemorySelect} />;
      case 'yearInReview':
        return <MemoryYearInReview onMemorySelect={handleMemorySelect} />;
      case 'collections':
        return <MemoriesCollection onMemorySelect={handleMemorySelect} />;
      default:
        return <MemoryOnThisDay onMemorySelect={handleMemorySelect} />;
    }
  };

  return (
    <div className="w-full">
      <div className="container-responsive mx-auto py-6">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2 dark:text-white">Memories</h1>
            <p className="text-gray-600 dark:text-gray-300">Look back on your favorite moments</p>
          </div>

          {/* Today's Date */}
          <Card className="mb-6 bg-gradient-to-r from-blue-500 to-purple-600 text-white">
            <CardContent className="p-6 text-center">
              <Calendar className="w-12 h-12 mx-auto mb-4" />
              <h2 className="text-2xl font-bold mb-2">{format(today, 'MMMM d, yyyy')}</h2>
              <p className="text-blue-100">See what happened on this day in previous years</p>
            </CardContent>
          </Card>

          {/* View Selector */}
          <div className="mb-6">
            <Tabs value={activeView} onValueChange={(value: 'onThisDay' | 'timeline' | 'yearInReview' | 'collections') => setActiveView(value)}>
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="onThisDay" className="flex items-center space-x-2">
                  <Calendar className="w-4 h-4" />
                  <span>On This Day</span>
                </TabsTrigger>
                <TabsTrigger value="timeline" className="flex items-center space-x-2">
                  <Clock className="w-4 h-4" />
                  <span>Timeline</span>
                </TabsTrigger>
                <TabsTrigger value="yearInReview" className="flex items-center space-x-2">
                  <Sparkles className="w-4 h-4" />
                  <span>Year in Review</span>
                </TabsTrigger>
                <TabsTrigger value="collections" className="flex items-center space-x-2">
                  <Bookmark className="w-4 h-4" />
                  <span>Collections</span>
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>

          {/* Memory Content */}
          {renderMemoryContent()}
        </div>
      </div>

      {/* Memory Detail Dialog */}
      <MemoryDetail
        memory={selectedMemory}
        isOpen={isMemoryDetailOpen}
        onClose={() => setIsMemoryDetailOpen(false)}
      />
    </div>
  );
};

export default Memories;