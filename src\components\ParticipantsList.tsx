import React from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Volume2, VolumeX } from 'lucide-react';

interface Participant {
  id: string;
  name: string;
  avatar: string;
  isHost: boolean;
  isMuted: boolean;
  isVideoOn: boolean;
  isSpeaking?: boolean;
}

interface ParticipantsListProps {
  participants: Participant[];
}

// Icon components
const VideoOff = ({ className }: { className?: string }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
    <path d="M10.66 6H14a2 2 0 0 1 2 2v2.34l1 1L22 8v8" />
    <path d="M16 16a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h2l10 10Z" />
    <line x1="2" y1="2" x2="22" y2="22" />
  </svg>
);

const Crown = ({ className }: { className?: string }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
    <path d="m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7z" />
    <path d="M3 16a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2" />
  </svg>
);

const ParticipantsList: React.FC<ParticipantsListProps> = ({ participants }) => {
  return (
    <div className="p-4 border-b dark:border-gray-700">
      <h3 className="font-semibold mb-3 dark:text-white">
        Participants ({participants.length})
      </h3>
      <div className="grid grid-cols-2 sm:grid-cols-4 lg:grid-cols-2 gap-2">
        {participants.map((participant) => (
          <div 
            key={participant.id} 
            className={`flex flex-col items-center p-2 rounded-lg transition-colors ${
              participant.isSpeaking 
                ? 'bg-blue-50 dark:bg-blue-900/20' 
                : 'bg-gray-50 dark:bg-gray-700'
            }`}
          >
            <div className="relative">
              <Avatar className="w-12 h-12">
                <AvatarImage src={participant.avatar} alt={participant.name} />
                <AvatarFallback>{participant.name.charAt(0)}</AvatarFallback>
              </Avatar>
              
              {/* Host Badge */}
              {participant.isHost && (
                <Badge className="absolute -top-1 -right-1 px-1 min-w-0 h-4 bg-blue-500">
                  <Crown className="w-3 h-3 text-white" />
                </Badge>
              )}
              
              {/* Speaking Indicator */}
              {participant.isSpeaking && (
                <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full flex items-center justify-center border-2 border-white dark:border-gray-800">
                  <Volume2 className="w-3 h-3 text-white" />
                </div>
              )}
            </div>
            
            <p className="text-xs mt-2 font-medium text-center line-clamp-1 dark:text-gray-200">
              {participant.name.replace(' (Host)', '')}
              {participant.isHost && ' 👑'}
            </p>
            
            {/* Status Indicators */}
            <div className="flex space-x-1 mt-1">
              {participant.isMuted && (
                <Badge 
                  variant="outline" 
                  className="px-1 min-w-0 h-5 dark:border-gray-600"
                  title="Muted"
                >
                  <VolumeX className="w-3 h-3 text-red-500" />
                </Badge>
              )}
              {!participant.isVideoOn && (
                <Badge 
                  variant="outline" 
                  className="px-1 min-w-0 h-5 dark:border-gray-600"
                  title="Video off"
                >
                  <VideoOff className="w-3 h-3 text-red-500" />
                </Badge>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ParticipantsList;