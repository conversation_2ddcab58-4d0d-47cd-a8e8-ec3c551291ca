import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'

// Simple component for testing setup
const TestComponent = () => {
  return (
    <div>
      <h1>Test Component</h1>
      <p>Hello, World!</p>
    </div>
  )
}

describe('Testing Setup', () => {
  it('can render a simple component', () => {
    render(<TestComponent />)
    expect(screen.getByText('Test Component')).toBeInTheDocument()
    expect(screen.getByText('Hello, World!')).toBeInTheDocument()
  })
  
  it('has access to testing library matchers', () => {
    render(<TestComponent />)
    const heading = screen.getByRole('heading', { name: 'Test Component' })
    expect(heading).toBeVisible()
    expect(heading).toHaveTextContent('Test Component')
  })
})
