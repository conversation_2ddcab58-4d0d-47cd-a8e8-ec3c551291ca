import React, { useEffect, useCallback } from 'react';
import { X, AlertTriangle, CheckCircle, Info, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import { cn } from '@/lib/utils';

// Base Modal Component
interface BaseModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  showCloseButton?: boolean;
  closeOnOverlayClick?: boolean;
  closeOnEscape?: boolean;
  className?: string;
}

export const BaseModal: React.FC<BaseModalProps> = ({
  isOpen,
  onClose,
  title,
  children,
  size = 'md',
  showCloseButton = true,
  closeOnOverlayClick = true,
  closeOnEscape = true,
  className
}) => {
  const sizeClasses = {
    sm: 'max-w-md',
    md: 'max-w-lg',
    lg: 'max-w-2xl',
    xl: 'max-w-4xl',
    full: 'max-w-full mx-4'
  };

  // Handle escape key
  useEffect(() => {
    if (!isOpen || !closeOnEscape) return;

    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, closeOnEscape, onClose]);

  // Prevent body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Overlay */}
      <div 
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={closeOnOverlayClick ? onClose : undefined}
      />
      
      {/* Modal Content */}
      <Card className={cn(
        "relative w-full max-h-[90vh] overflow-hidden",
        sizeClasses[size],
        className
      )}>
        {/* Header */}
        {(title || showCloseButton) && (
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
            {title && <CardTitle className="text-lg font-semibold">{title}</CardTitle>}
            {showCloseButton && (
              <Button variant="ghost" size="sm" onClick={onClose}>
                <X className="w-4 h-4" />
              </Button>
            )}
          </CardHeader>
        )}
        
        {/* Content */}
        <CardContent className="overflow-y-auto max-h-[calc(90vh-8rem)]">
          {children}
        </CardContent>
      </Card>
    </div>
  );
};

// Confirmation Modal
interface ConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  variant?: 'default' | 'destructive' | 'warning';
  isLoading?: boolean;
}

export const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  variant = 'default',
  isLoading = false
}) => {
  const getIcon = () => {
    switch (variant) {
      case 'destructive':
        return <AlertTriangle className="w-6 h-6 text-red-500" />;
      case 'warning':
        return <AlertCircle className="w-6 h-6 text-yellow-500" />;
      default:
        return <Info className="w-6 h-6 text-blue-500" />;
    }
  };

  const getConfirmButtonVariant = () => {
    switch (variant) {
      case 'destructive':
        return 'destructive';
      case 'warning':
        return 'default';
      default:
        return 'default';
    }
  };

  return (
    <BaseModal isOpen={isOpen} onClose={onClose} size="sm" title={title}>
      <div className="space-y-4">
        <div className="flex items-start space-x-3">
          {getIcon()}
          <p className="text-sm text-gray-600 dark:text-gray-300">{message}</p>
        </div>
        
        <div className="flex items-center justify-end space-x-2">
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            {cancelText}
          </Button>
          <Button 
            variant={getConfirmButtonVariant()} 
            onClick={onConfirm}
            disabled={isLoading}
          >
            {isLoading ? 'Loading...' : confirmText}
          </Button>
        </div>
      </div>
    </BaseModal>
  );
};

// Image Viewer Modal
interface ImageViewerModalProps {
  isOpen: boolean;
  onClose: () => void;
  images: Array<{
    id: string;
    url: string;
    caption?: string;
    alt?: string;
  }>;
  currentIndex: number;
  onIndexChange?: (index: number) => void;
}

export const ImageViewerModal: React.FC<ImageViewerModalProps> = ({
  isOpen,
  onClose,
  images,
  currentIndex,
  onIndexChange
}) => {
  const currentImage = images[currentIndex];

  const goToNext = useCallback(() => {
    const nextIndex = (currentIndex + 1) % images.length;
    onIndexChange?.(nextIndex);
  }, [currentIndex, images.length, onIndexChange]);

  const goToPrevious = useCallback(() => {
    const prevIndex = currentIndex === 0 ? images.length - 1 : currentIndex - 1;
    onIndexChange?.(prevIndex);
  }, [currentIndex, images.length, onIndexChange]);

  // Handle keyboard navigation
  useEffect(() => {
    if (!isOpen) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      switch (e.key) {
        case 'ArrowLeft':
          goToPrevious();
          break;
        case 'ArrowRight':
          goToNext();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, goToNext, goToPrevious]);

  if (!currentImage) return null;

  return (
    <BaseModal 
      isOpen={isOpen} 
      onClose={onClose} 
      size="full" 
      className="bg-black"
      showCloseButton={false}
    >
      <div className="relative h-full flex items-center justify-center">
        {/* Close Button */}
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="absolute top-4 right-4 z-10 text-white hover:bg-white/20"
        >
          <X className="w-6 h-6" />
        </Button>

        {/* Image Counter */}
        {images.length > 1 && (
          <div className="absolute top-4 left-4 z-10 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
            {currentIndex + 1} / {images.length}
          </div>
        )}

        {/* Navigation Buttons */}
        {images.length > 1 && (
          <>
            <Button
              variant="ghost"
              size="sm"
              onClick={goToPrevious}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 text-white hover:bg-white/20"
            >
              ←
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={goToNext}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 z-10 text-white hover:bg-white/20"
            >
              →
            </Button>
          </>
        )}

        {/* Image */}
        <img
          src={currentImage.url}
          alt={currentImage.alt || currentImage.caption || 'Image'}
          className="max-w-full max-h-full object-contain"
        />

        {/* Caption */}
        {currentImage.caption && (
          <div className="absolute bottom-4 left-4 right-4 bg-black/50 text-white p-4 rounded-lg">
            <p className="text-center">{currentImage.caption}</p>
          </div>
        )}
      </div>
    </BaseModal>
  );
};

// Share Modal
interface ShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  url: string;
  title?: string;
  description?: string;
}

export const ShareModal: React.FC<ShareModalProps> = ({
  isOpen,
  onClose,
  url,
  title,
  description
}) => {
  const shareOptions = [
    {
      name: 'Copy Link',
      action: () => {
        navigator.clipboard.writeText(url);
        onClose();
      }
    },
    {
      name: 'Facebook',
      action: () => {
        window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`, '_blank');
      }
    },
    {
      name: 'Twitter',
      action: () => {
        const text = title ? `${title} - ${url}` : url;
        window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}`, '_blank');
      }
    },
    {
      name: 'WhatsApp',
      action: () => {
        const text = title ? `${title} - ${url}` : url;
        window.open(`https://wa.me/?text=${encodeURIComponent(text)}`, '_blank');
      }
    },
    {
      name: 'Email',
      action: () => {
        const subject = title || 'Check this out';
        const body = description ? `${description}\n\n${url}` : url;
        window.open(`mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`);
      }
    }
  ];

  return (
    <BaseModal isOpen={isOpen} onClose={onClose} title="Share" size="sm">
      <div className="space-y-4">
        {title && (
          <div>
            <h3 className="font-medium">{title}</h3>
            {description && (
              <p className="text-sm text-gray-600 mt-1">{description}</p>
            )}
          </div>
        )}
        
        <div className="space-y-2">
          {shareOptions.map((option) => (
            <Button
              key={option.name}
              variant="outline"
              className="w-full justify-start"
              onClick={option.action}
            >
              {option.name}
            </Button>
          ))}
        </div>
      </div>
    </BaseModal>
  );
};

// Loading Modal
interface LoadingModalProps {
  isOpen: boolean;
  message?: string;
  progress?: number;
  showProgress?: boolean;
}

export const LoadingModal: React.FC<LoadingModalProps> = ({
  isOpen,
  message = 'Loading...',
  progress,
  showProgress = false
}) => {
  return (
    <BaseModal 
      isOpen={isOpen} 
      onClose={() => {}} 
      size="sm" 
      showCloseButton={false}
      closeOnOverlayClick={false}
      closeOnEscape={false}
    >
      <div className="text-center space-y-4">
        <div className="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto" />
        <p className="text-sm text-gray-600">{message}</p>
        
        {showProgress && typeof progress === 'number' && (
          <div className="space-y-2">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
              />
            </div>
            <p className="text-xs text-gray-500">{Math.round(progress)}%</p>
          </div>
        )}
      </div>
    </BaseModal>
  );
};

// Success Modal
interface SuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  message: string;
  actionText?: string;
  onAction?: () => void;
}

export const SuccessModal: React.FC<SuccessModalProps> = ({
  isOpen,
  onClose,
  title,
  message,
  actionText,
  onAction
}) => {
  return (
    <BaseModal isOpen={isOpen} onClose={onClose} title={title} size="sm">
      <div className="space-y-4">
        <div className="flex items-start space-x-3">
          <CheckCircle className="w-6 h-6 text-green-500 flex-shrink-0" />
          <p className="text-sm text-gray-600 dark:text-gray-300">{message}</p>
        </div>
        
        <div className="flex items-center justify-end space-x-2">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
          {actionText && onAction && (
            <Button onClick={onAction}>
              {actionText}
            </Button>
          )}
        </div>
      </div>
    </BaseModal>
  );
};
