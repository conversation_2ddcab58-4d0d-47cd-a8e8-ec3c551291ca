@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom animations for enhanced loading experience */
@keyframes spin-reverse {
  from {
    transform: rotate(360deg);
  }
  to {
    transform: rotate(0deg);
  }
}

.animate-spin-reverse {
  animation: spin-reverse 1s linear infinite;
}

/* Definition of the design system */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    
    --sidebar-background: 222.2 47.4% 11.2%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 217.2 91.2% 59.8%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 215.3 25% 26.7%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 215.3 25% 26.7%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-size: 1rem; /* 16px base font size */
    line-height: 1.5; /* 150% line height for body text */
    -webkit-tap-highlight-color: transparent;
    overflow-x: hidden; /* Prevent horizontal scrolling */
  }

  /* Typography Scale with Relative Units */
  h1 { 
    font-size: clamp(1.75rem, 4vw, 2.5rem); /* 28px - 40px */
    line-height: 1.2; /* 120% for headings */
    font-weight: 700;
  }
  
  h2 { 
    font-size: clamp(1.5rem, 3.5vw, 2rem); /* 24px - 32px */
    line-height: 1.2;
    font-weight: 600;
  }
  
  h3 { 
    font-size: clamp(1.25rem, 3vw, 1.5rem); /* 20px - 24px */
    line-height: 1.2;
    font-weight: 600;
  }

  /* Responsive Text Sizes */
  .text-responsive-sm { font-size: clamp(0.75rem, 2vw, 0.875rem); } /* 12px - 14px */
  .text-responsive-base { font-size: clamp(0.875rem, 2.5vw, 1rem); } /* 14px - 16px */
  .text-responsive-lg { font-size: clamp(1rem, 3vw, 1.125rem); } /* 16px - 18px */
}

/* RESPONSIVE BREAKPOINTS */
/* Mobile: 320px - 480px */
@media (max-width: 30rem) { /* 480px */
  .container-responsive {
    padding: 0.5rem; /* 8px */
    max-width: 100%;
  }
  
  .grid-responsive {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
  
  .flex-responsive {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .button-responsive {
    min-height: 2.75rem; /* 44px minimum touch target */
    min-width: 2.75rem;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
  }
  
  .text-mobile {
    font-size: 0.875rem; /* 14px minimum readable */
  }
}

/* Tablet: 481px - 768px */
@media (min-width: 30.0625rem) and (max-width: 48rem) { /* 481px - 768px */
  .container-responsive {
    padding: 0.75rem; /* 12px */
    max-width: 100%;
  }
  
  .grid-responsive {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
  
  .flex-responsive {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 1rem;
  }
  
  .button-responsive {
    min-height: 2.5rem; /* 40px */
    min-width: 2.5rem;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }
}

/* Desktop: 769px and above */
@media (min-width: 48.0625rem) { /* 769px+ */
  .container-responsive {
    padding: 1rem; /* 16px */
    max-width: 75rem; /* 1200px */
    margin: 0 auto;
  }
  
  .grid-responsive {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }
  
  .flex-responsive {
    flex-direction: row;
    gap: 1.5rem;
  }
  
  .button-responsive {
    min-height: 2.25rem; /* 36px */
    min-width: 2.25rem;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }
}

/* MAIN LAYOUT SYSTEM */
.main-layout {
  display: flex;
  max-width: 100%;
  position: relative;
  min-height: calc(100vh - 3.5rem); /* Account for header height (56px) */
  overflow-x: hidden;
}

.main-content {
  flex: 1;
  padding: 0;
  max-width: 100%;
  overflow-x: hidden;
  width: 100%;
}

/* Mobile: Full width content, no sidebars */
@media (max-width: 48rem) { /* 768px */
  .main-layout {
    flex-direction: column;
    width: 100%;
    overflow-x: hidden;
  }
  
  .main-content {
    width: 100%;
    margin: 0;
    overflow-x: hidden;
  }
}

/* Tablet: Left sidebar visible, right sidebar visible on Home page */
@media (min-width: 48.0625rem) and (max-width: 64rem) { /* 769px - 1024px */
  .main-layout.without-right-sidebar .main-content {
    margin-left: 16rem; /* 256px - left sidebar with full text */
    width: calc(100% - 16rem);
    overflow-x: hidden;
  }
  
  .main-layout.with-right-sidebar .main-content {
    margin-left: 16rem; /* 256px - left sidebar */
    margin-right: 16rem; /* 256px - right sidebar */
    width: calc(100% - 32rem);
    overflow-x: hidden;
  }
}

/* Desktop layouts with conditional right sidebar */
/* Without right sidebar - more space for content */
@media (min-width: 64.0625rem) { /* 1025px+ */
  .main-layout.without-right-sidebar .main-content {
    margin-left: 18rem; /* 288px */
    width: calc(100% - 18rem);
    max-width: calc(100% - 18rem);
    overflow-x: hidden;
  }
}

/* With right sidebar - balanced layout */
@media (min-width: 64.0625rem) and (max-width: 80rem) { /* 1025px - 1280px */
  .main-layout.with-right-sidebar .main-content {
    margin-left: 18rem; /* 288px */
    margin-right: 18rem; /* 288px */
    width: calc(100% - 36rem);
    overflow-x: hidden;
  }
}

/* Large Desktop: Wider content area with minimal sidebar spacing */
@media (min-width: 80.0625rem) and (max-width: 100rem) { /* 1281px - 1600px */
  .main-layout.with-right-sidebar .main-content {
    margin-left: 20rem; /* 320px - slightly wider sidebars */
    margin-right: 20rem; /* 320px */
    width: calc(100% - 40rem); /* More space for content */
    overflow-x: hidden;
  }
  
  .main-layout.without-right-sidebar .main-content {
    margin-left: 20rem; /* 320px */
    width: calc(100% - 20rem);
    max-width: calc(100% - 20rem);
    overflow-x: hidden;
  }
}

/* Extra Large Desktop: Maximum content width */
@media (min-width: 100.0625rem) { /* 1601px+ */
  .main-layout.with-right-sidebar .main-content {
    margin-left: 22rem; /* 352px */
    margin-right: 22rem; /* 352px */
    width: calc(100% - 44rem); /* Maximum content area */
    overflow-x: hidden;
  }
  
  .main-layout.without-right-sidebar .main-content {
    margin-left: 22rem; /* 352px */
    width: calc(100% - 22rem);
    max-width: calc(100% - 22rem);
    overflow-x: hidden;
  }
}

/* RESPONSIVE SIDEBAR STYLES */
.sidebar-responsive {
  position: fixed;
  left: 0;
  top: 3.5rem; /* 56px to provide proper clearance for header */
  height: calc(100vh - 3.5rem);
  background: white;
  border-right: 1px solid rgb(229 231 235);
  overflow-y: auto;
  z-index: 10;
  display: none; /* Hidden on mobile by default */
  width: 16rem; /* 256px default */
  transition: transform 0.3s ease-in-out;
}

.dark .sidebar-responsive {
  background: hsl(var(--sidebar-background));
  border-right-color: hsl(var(--sidebar-border));
}

/* Tablet: Full sidebar with text visible */
@media (min-width: 48.0625rem) and (max-width: 64rem) { /* 769px - 1024px */
  .sidebar-responsive {
    display: block;
    width: 16rem; /* 256px - full sidebar with text */
  }
}

/* Desktop: Standard sidebar - content-optimized */
@media (min-width: 64.0625rem) and (max-width: 80rem) { /* 1025px - 1280px */
  .sidebar-responsive {
    display: block;
    width: 18rem; /* 288px - optimized for desktop */
  }
}

/* Large Desktop: Optimized sidebar width */
@media (min-width: 80.0625rem) and (max-width: 100rem) { /* 1281px - 1600px */
  .sidebar-responsive {
    display: block;
    width: 20rem; /* 320px - slightly wider for better content balance */
  }
}

/* Extra Large Desktop: Maximum sidebar width */
@media (min-width: 100.0625rem) { /* 1601px+ */
  .sidebar-responsive {
    display: block;
    width: 22rem; /* 352px */
  }
}

/* RIGHT SIDEBAR RESPONSIVE STYLES - NOW INCLUDES TABLET */
.right-sidebar-responsive {
  position: fixed;
  right: 0;
  top: 3.5rem; /* 56px to match left sidebar */
  height: calc(100vh - 3.5rem);
  background: rgb(249 250 251);
  overflow-y: auto;
  z-index: 10;
  display: none; /* Hidden by default - only shown when with-right-sidebar class is present */
  width: 16rem; /* 256px default */
  transition: transform 0.3s ease-in-out;
}

.dark .right-sidebar-responsive {
  background: hsl(var(--sidebar-background));
}

/* Only show right sidebar when the layout has the with-right-sidebar class */
.main-layout.with-right-sidebar .right-sidebar-responsive {
  display: block !important;
}

/* Tablet: Show right sidebar on Home page */
@media (min-width: 48.0625rem) and (max-width: 64rem) { /* 769px - 1024px */
  .main-layout.with-right-sidebar .right-sidebar-responsive {
    display: block !important;
    width: 16rem; /* 256px - same as left sidebar for balance */
  }
}

/* Desktop: Show right sidebar - content-optimized */
@media (min-width: 64.0625rem) and (max-width: 80rem) { /* 1025px - 1280px */
  .main-layout.with-right-sidebar .right-sidebar-responsive {
    display: block !important;
    width: 18rem; /* 288px - optimized for desktop */
  }
}

/* Large Desktop: Optimized right sidebar width */
@media (min-width: 80.0625rem) and (max-width: 100rem) { /* 1281px - 1600px */
  .main-layout.with-right-sidebar .right-sidebar-responsive {
    display: block !important;
    width: 20rem; /* 320px - slightly wider for better balance */
  }
}

/* Extra Large Desktop: Maximum right sidebar width */
@media (min-width: 100.0625rem) { /* 1601px+ */
  .main-layout.with-right-sidebar .right-sidebar-responsive {
    display: block !important;
    width: 22rem; /* 352px */
  }
}

/* NEWS FEED RESPONSIVE OPTIMIZATIONS */
.news-feed-container {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  padding: 0.5rem;
}

@media (min-width: 30rem) { /* 480px+ */
  .news-feed-container {
    padding: 0.75rem;
  }
}

@media (min-width: 48.0625rem) { /* 769px+ */
  .news-feed-container {
    max-width: 100%;
    padding: 1rem;
  }
}

@media (min-width: 64.0625rem) { /* 1025px+ */
  .news-feed-container {
    max-width: 100%;
    padding: 1.5rem;
  }
}

/* Large Desktop: Wider news feed for better content utilization */
@media (min-width: 80.0625rem) { /* 1281px+ */
  .news-feed-container {
    max-width: 100%;
    padding: 2rem;
  }
}

/* RESPONSIVE COMPONENTS */
.card-responsive {
  padding: 0.75rem;
  margin-bottom: 0.75rem;
  border-radius: 0.5rem;
  width: 100%;
}

@media (min-width: 30rem) { /* 480px+ */
  .card-responsive {
    padding: 1rem;
    margin-bottom: 1rem;
  }
}

/* RESPONSIVE SPACING */
.spacing-responsive {
  margin: 0;
  padding: 0.75rem;
}

@media (min-width: 30rem) { /* 480px+ */
  .spacing-responsive {
    padding: 1rem;
  }
}

/* RESPONSIVE IMAGES */
.image-responsive {
  width: 100%;
  height: auto;
  max-width: 100%;
  object-fit: cover;
  border-radius: 0.25rem;
}

/* RESPONSIVE AVATARS */
.avatar-responsive {
  width: 2.25rem; /* 36px */
  height: 2.25rem;
  border-radius: 50%;
}

@media (min-width: 30rem) { /* 480px+ */
  .avatar-responsive {
    width: 2.5rem; /* 40px */
    height: 2.5rem;
  }
}

/* RESPONSIVE TOUCH TARGETS */
.touch-target {
  min-height: 2.75rem; /* 44px */
  min-width: 2.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  touch-action: manipulation;
}

/* RESPONSIVE FOCUS STATES */
.focus-responsive:focus {
  outline: 2px solid rgb(59 130 246);
  outline-offset: 2px;
}

@media (hover: hover) {
  .hover-responsive:hover {
    background-color: rgb(243 244 246);
    transition: background-color 0.2s ease;
  }
}

/* RESPONSIVE TYPOGRAPHY */
.text-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.text-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.text-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* RESPONSIVE UTILITIES */
.hide-mobile {
  display: none !important;
}

@media (min-width: 48rem) { /* 768px+ */
  .hide-mobile {
    display: block !important;
  }
}

.show-mobile {
  display: block !important;
}

@media (min-width: 48rem) { /* 768px+ */
  .show-mobile {
    display: none !important;
  }
}

/* RESPONSIVE SAFE AREAS */
.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom, 0);
}

.safe-area-top {
  padding-top: env(safe-area-inset-top, 0);
}

/* RESPONSIVE NAVIGATION */
.nav-responsive {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 50;
  background: white;
  border-top: 1px solid rgb(229 231 235);
  padding: 0.375rem;
  padding-bottom: env(safe-area-inset-bottom, 0.375rem);
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

.touch-action-manipulation {
  touch-action: manipulation;
}

.dark .nav-responsive {
  background: hsl(var(--background));
  border-top-color: hsl(var(--border));
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.2);
  padding-bottom: env(safe-area-inset-bottom, 0.375rem);
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

.touch-action-manipulation {
  touch-action: manipulation;
}

.dark .nav-responsive {
  background: hsl(var(--background));
  border-top-color: hsl(var(--border));
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.2);
  padding-bottom: env(safe-area-inset-bottom, 0.375rem);
}

@media (min-width: 48rem) { /* 768px+ */
  .nav-responsive {
    display: none;
  }
}

/* SCROLLBAR STYLES */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: rgb(209 213 219) rgb(243 244 246);
}

.dark .scrollbar-thin {
  scrollbar-color: rgb(75 85 99) rgb(31 41 55);
}

.scrollbar-thin::-webkit-scrollbar {
  width: 0.375rem; /* 6px */
  height: 0.375rem;
  display: none;
}

@media (min-width: 48rem) { /* 768px+ */
  .scrollbar-thin::-webkit-scrollbar {
    display: block;
  }
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgb(209 213 219);
  border-radius: 0.1875rem;
  border: 1px solid rgb(243 244 246);
}

.dark .scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgb(75 85 99);
  border: 1px solid rgb(31 41 55);
}

.scrollbar-thin::-webkit-scrollbar-track {
  background-color: rgb(243 244 246);
  border-radius: 0.1875rem;
}

.dark .scrollbar-thin::-webkit-scrollbar-track {
  background-color: rgb(31 41 55);
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: rgb(156 163 175);
}

.dark .scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: rgb(107 114 128);
}

/* ANIMATIONS */
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

/* REDUCED MOTION */
@media (prefers-reduced-motion: reduce) {
  .animate-pulse,
  .animate-spin {
    animation: none !important;
    transition: none !important;
  }
}

/* RESPONSIVE GRID LAYOUTS */
.grid-cols-responsive-1 {
  grid-template-columns: 1fr;
}

@media (min-width: 30rem) { /* 480px+ */
  .grid-cols-responsive-2 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 48rem) { /* 768px+ */
  .grid-cols-responsive-3 {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 64rem) { /* 1024px+ */
  .grid-cols-responsive-4 {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* RESPONSIVE CARD LAYOUTS */
.card-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 30rem) { /* 480px+ */
  .card-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }
}

@media (min-width: 48rem) { /* 768px+ */
  .card-grid {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  }
}

/* RESPONSIVE FLEX LAYOUTS */
.flex-responsive-wrap {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

@media (min-width: 30rem) { /* 480px+ */
  .flex-responsive-wrap {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 1rem;
  }
}

@media (max-width: 48rem) { /* 768px and below - mobile */
  .container-responsive {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
  
  .card-responsive {
    padding: 0.5rem;
    margin-bottom: 0.5rem;
    border-radius: 0.375rem;
  }
  
  .spacing-responsive {
    padding: 0.5rem;
  }
  
  .avatar-responsive {
    width: 2rem;
    height: 2rem;
  }
  
  .button-responsive {
    min-height: 2.5rem;
    min-width: 2.5rem;
    padding: 0.375rem 0.625rem;
    font-size: 0.75rem;
  }
}

/* RESPONSIVE FORM ELEMENTS */
.form-group-responsive {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

@media (min-width: 48rem) { /* 768px+ */
  .form-group-responsive {
    flex-direction: row;
    align-items: center;
  }
  
  .form-group-responsive > label {
    width: 8rem;
    margin-bottom: 0;
  }
}

/* RESPONSIVE PADDING AND MARGINS */
.p-responsive {
  padding: 0.75rem;
}

.m-responsive {
  margin: 0.75rem;
}

@media (min-width: 30rem) { /* 480px+ */
  .p-responsive {
    padding: 1rem;
  }
  
  .m-responsive {
    margin: 1rem;
  }
}

@media (min-width: 48rem) { /* 768px+ */
  .p-responsive {
    padding: 1.5rem;
  }
  
  .m-responsive {
    margin: 1.5rem;
  }
}

/* RESPONSIVE CONTAINER WIDTHS */
.container-sm {
  width: 100%;
  max-width: 640px;
  margin-left: auto;
  margin-right: auto;
}

.container-md {
  width: 100%;
  max-width: 768px;
  margin-left: auto;
  margin-right: auto;
}

.container-lg {
  width: 100%;
  max-width: 1024px;
  margin-left: auto;
  margin-right: auto;
}

.container-xl {
  width: 100%;
  max-width: 1280px;
  margin-left: auto;
  margin-right: auto;
}

/* RESPONSIVE ASPECT RATIOS */
.aspect-responsive-video {
  aspect-ratio: 16 / 9;
}

.aspect-responsive-square {
  aspect-ratio: 1 / 1;
}

.aspect-responsive-portrait {
  aspect-ratio: 3 / 4;
}

/* RESPONSIVE FONT SIZES */
.text-responsive-title {
  font-size: clamp(1.5rem, 5vw, 2.5rem);
  line-height: 1.2;
  font-weight: 700;
}

.text-responsive-subtitle {
  font-size: clamp(1.25rem, 4vw, 1.75rem);
  line-height: 1.3;
  font-weight: 600;
}

.text-responsive-heading {
  font-size: clamp(1.125rem, 3vw, 1.5rem);
  line-height: 1.3;
  font-weight: 600;
}

/* RESPONSIVE BUTTON SIZES */
.btn-responsive {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  min-height: 2.75rem;
  min-width: 2.75rem;
}

@media (min-width: 48rem) { /* 768px+ */
  .btn-responsive {
    padding: 0.625rem 1.25rem;
    font-size: 1rem;
  }
}

/* Performance optimizations */
img {
  content-visibility: auto;
  height: auto; /* Prevent layout shifts */
}

/* Fix iOS Safari issues */
@supports (-webkit-touch-callout: none) {
  .min-h-screen {
    min-height: -webkit-fill-available;
  }
  
  .h-screen {
    height: -webkit-fill-available;
  }
}

/* Word wrap for text in comments and posts */
.word-wrap {
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.overflow-wrap-anywhere {
  overflow-wrap: anywhere;
}

/* Add content-visibility for non-visible items */
.content-visibility-auto {
  content-visibility: auto;
  contain-intrinsic-size: auto;
}

/* Prevent layout shifts */
img, video, iframe {
  aspect-ratio: attr(width) / attr(height);
}

/* Add GPU acceleration for animations */
.gpu-accelerated {
  will-change: transform;
  backface-visibility: hidden;
  transform: translateZ(0);
}