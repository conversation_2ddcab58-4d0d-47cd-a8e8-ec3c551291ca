import React, { useState, useEffect, useCallback, useRef, memo } from 'react';
import { Send, Smile, Paperclip, Phone, Video, MoreHorizontal, Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { formatDistanceToNow } from 'date-fns';
import { motion, AnimatePresence } from 'framer-motion';
import EmojiPicker from '../EmojiPicker';
import { storage } from '@/lib/storage';
import { STORAGE_KEYS } from '@/lib/constants';

interface Message {
  id: string;
  senderId: string;
  content: string;
  timestamp: Date;
  type: 'text' | 'image' | 'file' | 'emoji';
  isRead: boolean;
  reactions?: { [emoji: string]: string[] };
  replyTo?: string;
}

interface Conversation {
  id: string;
  participants: {
    id: string;
    name: string;
    avatar: string;
    isOnline: boolean;
    lastSeen?: Date;
  }[];
  lastMessage?: Message;
  unreadCount: number;
  isTyping?: string[];
  isGroup: boolean;
  groupName?: string;
  groupAvatar?: string;
}

interface RealTimeMessagingProps {
  currentUserId: string;
  onVideoCall?: (conversationId: string) => void;
  onVoiceCall?: (conversationId: string) => void;
}

const RealTimeMessaging: React.FC<RealTimeMessagingProps> = memo(({
  currentUserId,
  onVideoCall,
  onVoiceCall
}) => {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [selectedConversation, setSelectedConversation] = useState<string | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isOnline, setIsOnline] = useState(true);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout>();

  // Load conversations and messages from storage
  useEffect(() => {
    const savedConversations = storage.get<Conversation[]>(STORAGE_KEYS.CHAT_HISTORY, []);
    if (savedConversations.length > 0) {
      setConversations(savedConversations);
    } else {
      // Initialize with mock conversations
      const mockConversations: Conversation[] = [
        {
          id: '1',
          participants: [
            {
              id: '2',
              name: 'Sarah Johnson',
              avatar: 'https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg?w=400&h=400&fit=crop&crop=face',
              isOnline: true
            }
          ],
          unreadCount: 2,
          isGroup: false,
          lastMessage: {
            id: '1',
            senderId: '2',
            content: 'Hey! How are you doing?',
            timestamp: new Date(Date.now() - 5 * 60 * 1000),
            type: 'text',
            isRead: false
          }
        },
        {
          id: '2',
          participants: [
            {
              id: '3',
              name: 'Mike Chen',
              avatar: 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?w=400&h=400&fit=crop&crop=face',
              isOnline: false,
              lastSeen: new Date(Date.now() - 30 * 60 * 1000)
            }
          ],
          unreadCount: 0,
          isGroup: false,
          lastMessage: {
            id: '2',
            senderId: currentUserId,
            content: 'Thanks for the help!',
            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
            type: 'text',
            isRead: true
          }
        },
        {
          id: '3',
          participants: [
            {
              id: '4',
              name: 'Emma Wilson',
              avatar: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?w=400&h=400&fit=crop&crop=face',
              isOnline: true
            },
            {
              id: '5',
              name: 'David Kim',
              avatar: 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?w=400&h=400&fit=crop&crop=face',
              isOnline: true
            }
          ],
          unreadCount: 5,
          isGroup: true,
          groupName: 'Project Team',
          groupAvatar: 'https://images.pexels.com/photos/3182812/pexels-photo-3182812.jpeg?w=400&h=400&fit=crop',
          lastMessage: {
            id: '3',
            senderId: '4',
            content: 'Meeting at 3 PM today',
            timestamp: new Date(Date.now() - 15 * 60 * 1000),
            type: 'text',
            isRead: false
          }
        }
      ];
      setConversations(mockConversations);
    }
  }, [currentUserId]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Simulate real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      // Simulate online status changes
      setConversations(prev => prev.map(conv => ({
        ...conv,
        participants: conv.participants.map(p => ({
          ...p,
          isOnline: Math.random() > 0.3
        }))
      })));
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  const handleSendMessage = useCallback(() => {
    if (!newMessage.trim() || !selectedConversation) return;

    const message: Message = {
      id: Date.now().toString(),
      senderId: currentUserId,
      content: newMessage,
      timestamp: new Date(),
      type: 'text',
      isRead: false
    };

    setMessages(prev => [...prev, message]);
    setNewMessage('');
    setIsTyping(false);

    // Update conversation's last message
    setConversations(prev => prev.map(conv => 
      conv.id === selectedConversation 
        ? { ...conv, lastMessage: message }
        : conv
    ));

    // Simulate response after a delay
    setTimeout(() => {
      const responseMessage: Message = {
        id: (Date.now() + 1).toString(),
        senderId: conversations.find(c => c.id === selectedConversation)?.participants[0]?.id || '2',
        content: getRandomResponse(),
        timestamp: new Date(),
        type: 'text',
        isRead: false
      };
      setMessages(prev => [...prev, responseMessage]);
    }, 1000 + Math.random() * 2000);
  }, [newMessage, selectedConversation, currentUserId, conversations]);

  const handleTyping = useCallback((value: string) => {
    setNewMessage(value);
    setIsTyping(true);

    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);
    }, 1000);
  }, []);

  const handleEmojiSelect = useCallback((emoji: string) => {
    setNewMessage(prev => prev + emoji);
    setShowEmojiPicker(false);
  }, []);

  const getRandomResponse = () => {
    const responses = [
      "That's great!",
      "I see, thanks for letting me know",
      "Sounds good to me",
      "Let me think about it",
      "Sure, no problem!",
      "I'll get back to you on that",
      "Awesome! 👍",
      "Thanks for the update"
    ];
    return responses[Math.floor(Math.random() * responses.length)];
  };

  const filteredConversations = conversations.filter(conv => {
    if (!searchQuery) return true;
    const searchLower = searchQuery.toLowerCase();
    return conv.participants.some(p => p.name.toLowerCase().includes(searchLower)) ||
           conv.groupName?.toLowerCase().includes(searchLower) ||
           conv.lastMessage?.content.toLowerCase().includes(searchLower);
  });

  const selectedConv = conversations.find(c => c.id === selectedConversation);

  return (
    <div className="flex h-full bg-white dark:bg-gray-900 rounded-lg shadow-lg overflow-hidden">
      {/* Conversations List */}
      <div className="w-1/3 border-r border-gray-200 dark:border-gray-700 flex flex-col">
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-3">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Messages</h2>
            <Badge variant={isOnline ? "default" : "secondary"} className="text-xs">
              {isOnline ? "Online" : "Offline"}
            </Badge>
          </div>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search conversations..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        
        <ScrollArea className="flex-1">
          <div className="p-2">
            {filteredConversations.map((conversation) => (
              <motion.div
                key={conversation.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className={`p-3 rounded-lg cursor-pointer transition-colors mb-2 ${
                  selectedConversation === conversation.id
                    ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800'
                    : 'hover:bg-gray-50 dark:hover:bg-gray-800'
                }`}
                onClick={() => setSelectedConversation(conversation.id)}
              >
                <div className="flex items-center space-x-3">
                  <div className="relative">
                    <Avatar className="w-12 h-12">
                      <AvatarImage 
                        src={conversation.isGroup ? conversation.groupAvatar : conversation.participants[0]?.avatar} 
                      />
                      <AvatarFallback>
                        {conversation.isGroup 
                          ? conversation.groupName?.charAt(0) 
                          : conversation.participants[0]?.name.charAt(0)
                        }
                      </AvatarFallback>
                    </Avatar>
                    {!conversation.isGroup && conversation.participants[0]?.isOnline && (
                      <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-white dark:border-gray-900"></div>
                    )}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium text-gray-900 dark:text-white truncate">
                        {conversation.isGroup ? conversation.groupName : conversation.participants[0]?.name}
                      </h3>
                      {conversation.lastMessage && (
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {formatDistanceToNow(conversation.lastMessage.timestamp, { addSuffix: true })}
                        </span>
                      )}
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <p className="text-sm text-gray-600 dark:text-gray-300 truncate">
                        {conversation.lastMessage?.content || 'No messages yet'}
                      </p>
                      {conversation.unreadCount > 0 && (
                        <Badge variant="destructive" className="text-xs">
                          {conversation.unreadCount}
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </ScrollArea>
      </div>

      {/* Chat Area */}
      <div className="flex-1 flex flex-col">
        {selectedConv ? (
          <>
            {/* Chat Header */}
            <div className="p-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Avatar className="w-10 h-10">
                    <AvatarImage 
                      src={selectedConv.isGroup ? selectedConv.groupAvatar : selectedConv.participants[0]?.avatar} 
                    />
                    <AvatarFallback>
                      {selectedConv.isGroup 
                        ? selectedConv.groupName?.charAt(0) 
                        : selectedConv.participants[0]?.name.charAt(0)
                      }
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="font-medium text-gray-900 dark:text-white">
                      {selectedConv.isGroup ? selectedConv.groupName : selectedConv.participants[0]?.name}
                    </h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {selectedConv.isGroup 
                        ? `${selectedConv.participants.length} members`
                        : selectedConv.participants[0]?.isOnline 
                          ? 'Active now' 
                          : `Last seen ${formatDistanceToNow(selectedConv.participants[0]?.lastSeen || new Date(), { addSuffix: true })}`
                      }
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onVoiceCall?.(selectedConv.id)}
                    className="text-gray-600 dark:text-gray-300"
                  >
                    <Phone className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onVideoCall?.(selectedConv.id)}
                    className="text-gray-600 dark:text-gray-300"
                  >
                    <Video className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-gray-600 dark:text-gray-300"
                  >
                    <MoreHorizontal className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Messages */}
            <ScrollArea className="flex-1 p-4">
              <div className="space-y-4">
                <AnimatePresence>
                  {messages.map((message) => (
                    <motion.div
                      key={message.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      className={`flex ${message.senderId === currentUserId ? 'justify-end' : 'justify-start'}`}
                    >
                      <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                        message.senderId === currentUserId
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white'
                      }`}>
                        <p className="text-sm">{message.content}</p>
                        <p className={`text-xs mt-1 ${
                          message.senderId === currentUserId ? 'text-blue-100' : 'text-gray-500 dark:text-gray-400'
                        }`}>
                          {formatDistanceToNow(message.timestamp, { addSuffix: true })}
                        </p>
                      </div>
                    </motion.div>
                  ))}
                </AnimatePresence>
                
                {isTyping && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="flex justify-start"
                  >
                    <div className="bg-gray-100 dark:bg-gray-700 px-4 py-2 rounded-lg">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                      </div>
                    </div>
                  </motion.div>
                )}
                <div ref={messagesEndRef} />
              </div>
            </ScrollArea>

            {/* Message Input */}
            <div className="p-4 border-t border-gray-200 dark:border-gray-700">
              <div className="flex items-center space-x-2">
                <div className="relative">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                    className="text-gray-600 dark:text-gray-300"
                  >
                    <Smile className="w-4 h-4" />
                  </Button>
                  {showEmojiPicker && (
                    <div className="absolute bottom-full left-0 mb-2">
                      <EmojiPicker onEmojiSelect={handleEmojiSelect} />
                    </div>
                  )}
                </div>
                
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-gray-600 dark:text-gray-300"
                >
                  <Paperclip className="w-4 h-4" />
                </Button>
                
                <div className="flex-1">
                  <Input
                    placeholder="Type a message..."
                    value={newMessage}
                    onChange={(e) => handleTyping(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                    className="border-0 focus:ring-0 bg-gray-100 dark:bg-gray-700"
                  />
                </div>
                
                <Button
                  onClick={handleSendMessage}
                  disabled={!newMessage.trim()}
                  className="bg-blue-500 hover:bg-blue-600 text-white"
                >
                  <Send className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                <MessageCircle className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                Select a conversation
              </h3>
              <p className="text-gray-500 dark:text-gray-400">
                Choose a conversation from the list to start messaging
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
});

RealTimeMessaging.displayName = 'RealTimeMessaging';

export default RealTimeMessaging;
