export const WATCH_TOGETHER_CONFIG = {
  CONTROLS_HIDE_DELAY: 3000,
  CHAT_SIMULATION_INTERVAL: 8000,
  SPEAKING_INDICATOR_DURATION: 2000,
  MAX_CHAT_MESSAGES: 100,
  VIDEO_SEEK_STEP: 10, // seconds
  AUTO_SCROLL_DELAY: 100,
} as const;

export const MOCK_CHAT_MESSAGES = [
  "This part is so good!",
  "Wait, did you see that?",
  "I can't believe this scene, wow!",
  "I've watched this 5 times already",
  "The soundtrack is amazing",
  "Who's your favorite character?",
  "This reminds me of that other movie we watched",
  "Let's watch the sequel next time",
  "The cinematography is incredible",
  "This actor is so talented",
  "I love this director's style",
  "The plot twist was amazing!",
  "This scene gives me chills",
  "The special effects are mind-blowing"
] as const;

export const VIDEO_ERROR_MESSAGES = {
  [MediaError.MEDIA_ERR_ABORTED]: 'Video playback was aborted',
  [MediaError.MEDIA_ERR_NETWORK]: 'Network error occurred while loading video',
  [MediaError.MEDIA_ERR_DECODE]: 'Video decoding error occurred',
  [MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED]: 'Video format not supported'
} as const;

export const KEYBOARD_SHORTCUTS = {
  PLAY_PAUSE: ' ', // Spacebar
  MUTE: 'm',
  FULLSCREEN: 'f',
  SEEK_FORWARD: 'ArrowRight',
  SEEK_BACKWARD: 'ArrowLeft',
  VOLUME_UP: 'ArrowUp',
  VOLUME_DOWN: 'ArrowDown'
} as const;

export const PARTICIPANT_ROLES = {
  HOST: 'host',
  PARTICIPANT: 'participant',
  MODERATOR: 'moderator'
} as const;