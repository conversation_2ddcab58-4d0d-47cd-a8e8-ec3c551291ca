import { useState, useEffect, useCallback, useRef } from 'react';
import FileUploadService, { UploadFile, UploadOptions } from '@/services/FileUploadService';

interface UseFileUploadReturn {
  // State
  uploads: UploadFile[];
  isUploading: boolean;
  totalProgress: number;
  completedUploads: UploadFile[];
  failedUploads: UploadFile[];
  
  // Actions
  uploadFiles: (files: FileList | File[], options?: UploadOptions) => Promise<UploadFile[]>;
  retryUpload: (uploadId: string) => Promise<void>;
  cancelUpload: (uploadId: string) => void;
  removeUpload: (uploadId: string) => void;
  clearCompleted: () => void;
  clearAll: () => void;
  
  // Utilities
  getUpload: (uploadId: string) => UploadFile | undefined;
  getUploadsByStatus: (status: string) => UploadFile[];
  getTotalSize: () => number;
  getCompletedSize: () => number;
}

export const useFileUpload = (defaultOptions?: UploadOptions): UseFileUploadReturn => {
  const [uploads, setUploads] = useState<UploadFile[]>([]);
  const uploadService = useRef(FileUploadService.getInstance());

  // Subscribe to upload events
  useEffect(() => {
    const service = uploadService.current;

    const handleUploadStarted = (uploadFile: UploadFile) => {
      setUploads(prev => prev.map(f => f.id === uploadFile.id ? uploadFile : f));
    };

    const handleUploadProgress = (uploadFile: UploadFile) => {
      setUploads(prev => prev.map(f => f.id === uploadFile.id ? uploadFile : f));
    };

    const handleUploadCompleted = (uploadFile: UploadFile) => {
      setUploads(prev => prev.map(f => f.id === uploadFile.id ? uploadFile : f));
    };

    const handleUploadError = (uploadFile: UploadFile) => {
      setUploads(prev => prev.map(f => f.id === uploadFile.id ? uploadFile : f));
    };

    const handleUploadCancelled = (uploadFile: UploadFile) => {
      setUploads(prev => prev.map(f => f.id === uploadFile.id ? uploadFile : f));
    };

    // Subscribe to events
    service.on('upload_started', handleUploadStarted);
    service.on('upload_progress', handleUploadProgress);
    service.on('upload_completed', handleUploadCompleted);
    service.on('upload_error', handleUploadError);
    service.on('upload_cancelled', handleUploadCancelled);

    return () => {
      // Cleanup event listeners
      service.off('upload_started', handleUploadStarted);
      service.off('upload_progress', handleUploadProgress);
      service.off('upload_completed', handleUploadCompleted);
      service.off('upload_error', handleUploadError);
      service.off('upload_cancelled', handleUploadCancelled);
    };
  }, []);

  // Computed values
  const isUploading = uploads.some(upload => upload.status === 'uploading');
  
  const totalProgress = uploads.length > 0 
    ? uploads.reduce((sum, upload) => sum + upload.progress, 0) / uploads.length
    : 0;

  const completedUploads = uploads.filter(upload => upload.status === 'completed');
  const failedUploads = uploads.filter(upload => upload.status === 'error');

  // Actions
  const uploadFiles = useCallback(async (
    files: FileList | File[], 
    options?: UploadOptions
  ): Promise<UploadFile[]> => {
    const opts = { ...defaultOptions, ...options };
    
    const uploadFiles = await uploadService.current.uploadFiles(files, opts);
    setUploads(prev => [...uploadFiles, ...prev]);
    return uploadFiles;
  }, [defaultOptions]);

  const retryUpload = useCallback(async (uploadId: string): Promise<void> => {
    const upload = uploads.find(u => u.id === uploadId);
    if (upload) {
      await uploadService.current.retryUpload(uploadId, defaultOptions);
    }
  }, [uploads, defaultOptions]);

  const cancelUpload = useCallback((uploadId: string): void => {
    uploadService.current.cancelUpload(uploadId);
  }, []);

  const removeUpload = useCallback((uploadId: string): void => {
    setUploads(prev => prev.filter(upload => upload.id !== uploadId));
  }, []);

  const clearCompleted = useCallback((): void => {
    setUploads(prev => prev.filter(upload => upload.status !== 'completed'));
    uploadService.current.clearCompleted();
  }, []);

  const clearAll = useCallback((): void => {
    setUploads([]);
  }, []);

  // Utilities
  const getUpload = useCallback((uploadId: string): UploadFile | undefined => {
    return uploads.find(upload => upload.id === uploadId);
  }, [uploads]);

  const getUploadsByStatus = useCallback((status: string): UploadFile[] => {
    return uploads.filter(upload => upload.status === status);
  }, [uploads]);

  const getTotalSize = useCallback((): number => {
    return uploads.reduce((total, upload) => total + upload.size, 0);
  }, [uploads]);

  const getCompletedSize = useCallback((): number => {
    return completedUploads.reduce((total, upload) => total + upload.size, 0);
  }, [completedUploads]);

  return {
    // State
    uploads,
    isUploading,
    totalProgress,
    completedUploads,
    failedUploads,
    
    // Actions
    uploadFiles,
    retryUpload,
    cancelUpload,
    removeUpload,
    clearCompleted,
    clearAll,
    
    // Utilities
    getUpload,
    getUploadsByStatus,
    getTotalSize,
    getCompletedSize
  };
};

// Hook for drag and drop functionality
export const useDragAndDrop = (
  onFilesDropped: (files: File[]) => void,
  options?: {
    accept?: string[];
    maxFiles?: number;
    disabled?: boolean;
  }
) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [isDragActive, setIsDragActive] = useState(false);

  const handleDragEnter = useCallback((e: DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (options?.disabled) return;
    
    setIsDragActive(true);
    
    if (e.dataTransfer?.items && e.dataTransfer.items.length > 0) {
      setIsDragOver(true);
    }
  }, [options?.disabled]);

  const handleDragLeave = useCallback((e: DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (options?.disabled) return;
    
    setIsDragOver(false);
    setIsDragActive(false);
  }, [options?.disabled]);

  const handleDragOver = useCallback((e: DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (options?.disabled) return;
    
    setIsDragOver(true);
  }, [options?.disabled]);

  const handleDrop = useCallback((e: DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    setIsDragOver(false);
    setIsDragActive(false);
    
    if (options?.disabled) return;
    
    const files = Array.from(e.dataTransfer?.files || []);
    
    if (files.length === 0) return;
    
    // Filter by accepted file types
    let filteredFiles = files;
    if (options?.accept && options.accept.length > 0) {
      filteredFiles = files.filter(file => {
        return options.accept!.some(acceptType => {
          if (acceptType.endsWith('/*')) {
            const category = acceptType.slice(0, -2);
            return file.type.startsWith(category);
          }
          return file.type === acceptType;
        });
      });
    }
    
    // Limit number of files
    if (options?.maxFiles && filteredFiles.length > options.maxFiles) {
      filteredFiles = filteredFiles.slice(0, options.maxFiles);
    }
    
    if (filteredFiles.length > 0) {
      onFilesDropped(filteredFiles);
    }
  }, [onFilesDropped, options]);

  // Set up global drag and drop listeners
  useEffect(() => {
    if (options?.disabled) return;

    document.addEventListener('dragenter', handleDragEnter);
    document.addEventListener('dragleave', handleDragLeave);
    document.addEventListener('dragover', handleDragOver);
    document.addEventListener('drop', handleDrop);

    return () => {
      document.removeEventListener('dragenter', handleDragEnter);
      document.removeEventListener('dragleave', handleDragLeave);
      document.removeEventListener('dragover', handleDragOver);
      document.removeEventListener('drop', handleDrop);
    };
  }, [handleDragEnter, handleDragLeave, handleDragOver, handleDrop, options?.disabled]);

  return {
    isDragOver,
    isDragActive,
    dragHandlers: {
      onDragEnter: handleDragEnter,
      onDragLeave: handleDragLeave,
      onDragOver: handleDragOver,
      onDrop: handleDrop
    }
  };
};

// Hook for file validation
export const useFileValidation = () => {
  const validateFile = useCallback((
    file: File,
    options: {
      maxSize?: number;
      allowedTypes?: string[];
      minWidth?: number;
      minHeight?: number;
      maxWidth?: number;
      maxHeight?: number;
    }
  ): Promise<{ isValid: boolean; errors: string[] }> => {
    return new Promise((resolve) => {
      const errors: string[] = [];

      // Check file size
      if (options.maxSize && file.size > options.maxSize) {
        errors.push(`File size exceeds ${formatFileSize(options.maxSize)}`);
      }

      // Check file type
      if (options.allowedTypes && !isFileTypeAllowed(file, options.allowedTypes)) {
        errors.push('File type not allowed');
      }

      // For images, check dimensions
      if (file.type.startsWith('image/') && (
        options.minWidth || options.minHeight || options.maxWidth || options.maxHeight
      )) {
        const img = new Image();
        img.onload = () => {
          if (options.minWidth && img.width < options.minWidth) {
            errors.push(`Image width must be at least ${options.minWidth}px`);
          }
          if (options.minHeight && img.height < options.minHeight) {
            errors.push(`Image height must be at least ${options.minHeight}px`);
          }
          if (options.maxWidth && img.width > options.maxWidth) {
            errors.push(`Image width must not exceed ${options.maxWidth}px`);
          }
          if (options.maxHeight && img.height > options.maxHeight) {
            errors.push(`Image height must not exceed ${options.maxHeight}px`);
          }
          
          resolve({ isValid: errors.length === 0, errors });
          URL.revokeObjectURL(img.src);
        };
        img.onerror = () => {
          errors.push('Invalid image file');
          resolve({ isValid: false, errors });
        };
        img.src = URL.createObjectURL(file);
      } else {
        resolve({ isValid: errors.length === 0, errors });
      }
    });
  }, []);

  const validateFiles = useCallback(async (
    files: File[],
    options: Parameters<typeof validateFile>[1]
  ): Promise<{ valid: File[]; invalid: { file: File; errors: string[] }[] }> => {
    const results = await Promise.all(
      files.map(async (file) => {
        const validation = await validateFile(file, options);
        return { file, validation };
      })
    );

    const valid = results
      .filter(({ validation }) => validation.isValid)
      .map(({ file }) => file);

    const invalid = results
      .filter(({ validation }) => !validation.isValid)
      .map(({ file, validation }) => ({ file, errors: validation.errors }));

    return { valid, invalid };
  }, [validateFile]);

  return { validateFile, validateFiles };
};

// Utility functions
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const isFileTypeAllowed = (file: File, allowedTypes: string[]): boolean => {
  return allowedTypes.some(type => {
    if (type.endsWith('/*')) {
      const category = type.slice(0, -2);
      return file.type.startsWith(category);
    }
    return file.type === type;
  });
};
