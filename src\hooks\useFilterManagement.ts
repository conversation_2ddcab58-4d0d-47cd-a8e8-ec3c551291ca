import { useState, useCallback, useMemo } from 'react';
import { toast } from 'sonner';

// Filter state interface
export interface FilterState {
  creator: string;
  dateRange: string;
  location: string;
  liked: boolean;
  saved: boolean;
}

const initialFilterState: FilterState = {
  creator: '',
  dateRange: 'all',
  location: '',
  liked: false,
  saved: false,
};

export const useFilterManagement = () => {
  const [filters, setFilters] = useState<FilterState>(initialFilterState);
  const [showFilters, setShowFilters] = useState(false);

  // Handle applying filters with consolidated state
  const handleApplyFilters = useCallback((filterData: Partial<FilterState>) => {
    setFilters(prev => ({ ...prev, ...filterData }));
    setShowFilters(false);
    toast.success('Filters applied successfully!');
  }, []);

  // Handle clearing filters
  const handleClearFilters = useCallback(() => {
    setFilters(initialFilterState);
    setShowFilters(false);
    toast.success('Filters cleared!');
  }, []);

  // Update individual filter
  const updateFilter = useCallback(<K extends keyof FilterState>(key: K, value: FilterState[K]) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  }, []);

  // Apply filters to posts
  const applyFiltersToPost = useCallback((posts: unknown[], activeTab: string, sortBy: string) => {
    let filtered = [...posts];

    // Filter by tab
    if (activeTab === 'following') {
      // In a real app, this would filter by followed users
      filtered = filtered.filter(_post => Math.random() > 0.3);
    }

    // Apply filters using consolidated filter state
    if (filters.creator) {
      filtered = filtered.filter(post => 
        post.profiles?.full_name?.toLowerCase().includes(filters.creator.toLowerCase())
      );
    }

    if (filters.location) {
      filtered = filtered.filter(post => 
        post.location?.toLowerCase().includes(filters.location.toLowerCase())
      );
    }

    if (filters.liked) {
      filtered = filtered.filter(post => post.user_has_liked);
    }

    if (filters.saved) {
      filtered = filtered.filter(post => post.user_has_saved);
    }

    // Apply date range filter
    if (filters.dateRange !== 'all') {
      const now = new Date();
      const filterDate = new Date();
      
      switch (filters.dateRange) {
        case 'today':
          filterDate.setHours(0, 0, 0, 0);
          break;
        case 'week':
          filterDate.setDate(now.getDate() - 7);
          break;
        case 'month':
          filterDate.setMonth(now.getMonth() - 1);
          break;
      }
      
      filtered = filtered.filter(post => new Date(post.created_at) >= filterDate);
    }

    // Sort posts
    switch (sortBy) {
      case 'recent':
        filtered.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
        break;
      case 'popular':
        filtered.sort((a, b) => (b.likes_count || 0) - (a.likes_count || 0));
        break;
      case 'comments':
        filtered.sort((a, b) => (b.comments_count || 0) - (a.comments_count || 0));
        break;
    }

    return filtered;
  }, [filters]);

  // Check if any filters are active
  const hasActiveFilters = useMemo(() => {
    return filters.creator !== '' ||
           filters.dateRange !== 'all' ||
           filters.location !== '' ||
           filters.liked ||
           filters.saved;
  }, [filters]);

  return {
    filters,
    showFilters,
    setShowFilters,
    handleApplyFilters,
    handleClearFilters,
    updateFilter,
    applyFiltersToPost,
    hasActiveFilters,
  };
};