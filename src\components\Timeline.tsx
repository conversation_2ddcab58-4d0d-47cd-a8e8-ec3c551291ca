import React, { useState, use<PERSON><PERSON>back, useMemo } from 'react';
import { Calendar, Clock, MapPin, Users, MessageCircle, Share, ChevronDown, ChevronUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion, AnimatePresence } from 'framer-motion';
import { format, isToday, isYesterday, isThisWeek, isThisMonth, isThisYear } from 'date-fns';
import OptimizedImage from './OptimizedImage';
import ReactionPicker from './ReactionPicker';

interface TimelinePost {
  id: string;
  author: {
    id: string;
    name: string;
    avatar: string;
    isVerified?: boolean;
  };
  content: string;
  images?: string[];
  video?: string;
  location?: {
    name: string;
    coordinates: { lat: number; lng: number };
  };
  feeling?: {
    emoji: string;
    text: string;
  };
  taggedUsers?: Array<{
    id: string;
    name: string;
  }>;
  createdAt: Date;
  reactions: {
    like: number;
    love: number;
    haha: number;
    wow: number;
    sad: number;
    angry: number;
  };
  comments: number;
  shares: number;
  privacy: 'public' | 'friends' | 'private';
  type: 'status' | 'photo' | 'video' | 'checkin' | 'life_event';
  lifeEvent?: {
    type: string;
    icon: string;
    description: string;
  };
}

interface TimelineProps {
  userId?: string;
  posts: TimelinePost[];
  onLoadMore?: () => void;
  isLoading?: boolean;
}

const Timeline: React.FC<TimelineProps> = ({ 
  userId: _userId, 
  posts, 
  onLoadMore, 
  isLoading = false 
}) => {
  const [selectedYear, setSelectedYear] = useState<string>('all');
  const [selectedType, setSelectedType] = useState<string>('all');
  const [expandedPosts, setExpandedPosts] = useState<Set<string>>(new Set());
  const [activeTab, setActiveTab] = useState<string>('timeline');

  // Group posts by time periods
  const groupedPosts = useMemo(() => {
    let filteredPosts = posts;

    // Filter by year
    if (selectedYear !== 'all') {
      const year = parseInt(selectedYear);
      filteredPosts = filteredPosts.filter(post => 
        post.createdAt.getFullYear() === year
      );
    }

    // Filter by type
    if (selectedType !== 'all') {
      filteredPosts = filteredPosts.filter(post => post.type === selectedType);
    }

    // Group by time periods
    const groups: { [key: string]: TimelinePost[] } = {};
    
    filteredPosts.forEach(post => {
      let groupKey: string;
      
      if (isToday(post.createdAt)) {
        groupKey = 'Today';
      } else if (isYesterday(post.createdAt)) {
        groupKey = 'Yesterday';
      } else if (isThisWeek(post.createdAt)) {
        groupKey = 'This Week';
      } else if (isThisMonth(post.createdAt)) {
        groupKey = 'This Month';
      } else if (isThisYear(post.createdAt)) {
        groupKey = format(post.createdAt, 'MMMM yyyy');
      } else {
        groupKey = format(post.createdAt, 'yyyy');
      }
      
      if (!groups[groupKey]) {
        groups[groupKey] = [];
      }
      groups[groupKey].push(post);
    });

    // Sort groups by most recent first
    const sortedGroups = Object.entries(groups).sort(([a], [b]) => {
      const order = ['Today', 'Yesterday', 'This Week', 'This Month'];
      const aIndex = order.indexOf(a);
      const bIndex = order.indexOf(b);
      
      if (aIndex !== -1 && bIndex !== -1) {
        return aIndex - bIndex;
      }
      if (aIndex !== -1) return -1;
      if (bIndex !== -1) return 1;
      
      return b.localeCompare(a);
    });

    return sortedGroups;
  }, [posts, selectedYear, selectedType]);

  // Get available years from posts
  const availableYears = useMemo(() => {
    const years = [...new Set(posts.map(post => post.createdAt.getFullYear()))];
    return years.sort((a, b) => b - a);
  }, [posts]);

  const togglePostExpansion = useCallback((postId: string) => {
    setExpandedPosts(prev => {
      const newSet = new Set(prev);
      if (newSet.has(postId)) {
        newSet.delete(postId);
      } else {
        newSet.add(postId);
      }
      return newSet;
    });
  }, []);

  const getPostTypeIcon = (type: string) => {
    switch (type) {
      case 'photo': return '📷';
      case 'video': return '🎥';
      case 'checkin': return '📍';
      case 'life_event': return '🎉';
      default: return '💭';
    }
  };

  const getPostTypeColor = (type: string) => {
    switch (type) {
      case 'photo': return 'bg-green-100 text-green-800';
      case 'video': return 'bg-purple-100 text-purple-800';
      case 'checkin': return 'bg-blue-100 text-blue-800';
      case 'life_event': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatPostTime = (date: Date) => {
    if (isToday(date)) {
      return format(date, 'h:mm a');
    } else if (isYesterday(date)) {
      return `Yesterday at ${format(date, 'h:mm a')}`;
    } else if (isThisWeek(date)) {
      return format(date, 'EEEE \\a\\t h:mm a');
    } else {
      return format(date, 'MMM d, yyyy \\a\\t h:mm a');
    }
  };

  const renderPost = (post: TimelinePost) => {
    const isExpanded = expandedPosts.has(post.id);
    const shouldTruncate = post.content.length > 200;
    const displayContent = shouldTruncate && !isExpanded 
      ? post.content.substring(0, 200) + '...' 
      : post.content;

    return (
      <motion.div
        key={post.id}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="relative"
      >
        {/* Timeline connector */}
        <div className="absolute left-6 top-16 bottom-0 w-0.5 bg-gray-200" />
        
        <Card className="ml-16 mb-6 hover:shadow-md transition-shadow">
          <CardHeader className="pb-3">
            <div className="flex items-start space-x-3">
              {/* Timeline dot */}
              <div className="absolute left-4 w-4 h-4 bg-blue-500 rounded-full border-4 border-white shadow-sm" />
              
              <Avatar className="w-10 h-10">
                <AvatarImage src={post.author.avatar} />
                <AvatarFallback>{post.author.name.charAt(0)}</AvatarFallback>
              </Avatar>
              
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <h3 className="font-semibold">{post.author.name}</h3>
                  {post.author.isVerified && (
                    <Badge variant="secondary" className="text-xs">✓</Badge>
                  )}
                  <Badge className={`text-xs ${getPostTypeColor(post.type)}`}>
                    {getPostTypeIcon(post.type)} {post.type.replace('_', ' ')}
                  </Badge>
                </div>
                
                <div className="flex items-center space-x-2 text-sm text-gray-500 mt-1">
                  <Clock className="w-3 h-3" />
                  <span>{formatPostTime(post.createdAt)}</span>
                  
                  {post.location && (
                    <>
                      <span>•</span>
                      <MapPin className="w-3 h-3" />
                      <span>{post.location.name}</span>
                    </>
                  )}
                  
                  {post.feeling && (
                    <>
                      <span>•</span>
                      <span>{post.feeling.emoji} feeling {post.feeling.text}</span>
                    </>
                  )}
                  
                  {post.taggedUsers && post.taggedUsers.length > 0 && (
                    <>
                      <span>•</span>
                      <Users className="w-3 h-3" />
                      <span>with {post.taggedUsers.map(user => user.name).join(', ')}</span>
                    </>
                  )}
                </div>
              </div>
            </div>
          </CardHeader>
          
          <CardContent className="pt-0">
            {/* Life event special display */}
            {post.lifeEvent && (
              <div className="mb-4 p-3 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200">
                <div className="flex items-center space-x-2 mb-2">
                  <span className="text-2xl">{post.lifeEvent.icon}</span>
                  <span className="font-medium text-blue-900">{post.lifeEvent.type}</span>
                </div>
                <p className="text-blue-800">{post.lifeEvent.description}</p>
              </div>
            )}
            
            {/* Post content */}
            {post.content && (
              <div className="mb-4">
                <p className="whitespace-pre-wrap">{displayContent}</p>
                {shouldTruncate && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => togglePostExpansion(post.id)}
                    className="mt-2 p-0 h-auto text-blue-600 hover:text-blue-800"
                  >
                    {isExpanded ? (
                      <>
                        <ChevronUp className="w-4 h-4 mr-1" />
                        Show less
                      </>
                    ) : (
                      <>
                        <ChevronDown className="w-4 h-4 mr-1" />
                        See more
                      </>
                    )}
                  </Button>
                )}
              </div>
            )}
            
            {/* Post media */}
            {post.images && post.images.length > 0 && (
              <div className="mb-4">
                <div className={`grid gap-2 ${
                  post.images.length === 1 ? 'grid-cols-1' :
                  post.images.length === 2 ? 'grid-cols-2' :
                  post.images.length === 3 ? 'grid-cols-2' :
                  'grid-cols-2'
                }`}>
                  {post.images.slice(0, 4).map((image, index) => (
                    <div key={index} className={`relative ${
                      post.images!.length === 3 && index === 0 ? 'row-span-2' : ''
                    }`}>
                      <OptimizedImage
                        src={image}
                        alt={`Post image ${index + 1}`}
                        className="w-full h-48 object-cover rounded-lg"
                      />
                      {post.images!.length > 4 && index === 3 && (
                        <div className="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center">
                          <span className="text-white text-xl font-semibold">
                            +{post.images!.length - 4}
                          </span>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            {post.video && (
              <div className="mb-4">
                <video
                  controls
                  className="w-full rounded-lg"
                  poster={post.images?.[0]}
                >
                  <source src={post.video} type="video/mp4" />
                </video>
              </div>
            )}
            
            {/* Reactions and stats */}
            <div className="flex items-center justify-between pt-3 border-t border-gray-100">
              <div className="flex items-center space-x-4">
                <ReactionPicker
                  onReaction={(reaction) => console.log('Reaction:', reaction)}
                  currentReaction={null}
                />
                
                <Button variant="ghost" size="sm" className="text-gray-600">
                  <MessageCircle className="w-4 h-4 mr-1" />
                  {post.comments}
                </Button>
                
                <Button variant="ghost" size="sm" className="text-gray-600">
                  <Share className="w-4 h-4 mr-1" />
                  {post.shares}
                </Button>
              </div>
              
              <div className="text-sm text-gray-500">
                {Object.values(post.reactions).reduce((a, b) => a + b, 0)} reactions
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    );
  };

  return (
    <div className="max-w-2xl mx-auto">
      {/* Timeline header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-4">Timeline</h1>
        
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="timeline">Timeline</TabsTrigger>
            <TabsTrigger value="photos">Photos</TabsTrigger>
            <TabsTrigger value="videos">Videos</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>
      
      {/* Filters */}
      <div className="flex space-x-4 mb-6">
        <Select value={selectedYear} onValueChange={setSelectedYear}>
          <SelectTrigger className="w-32">
            <SelectValue placeholder="Year" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Years</SelectItem>
            {availableYears.map(year => (
              <SelectItem key={year} value={year.toString()}>
                {year}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        <Select value={selectedType} onValueChange={setSelectedType}>
          <SelectTrigger className="w-40">
            <SelectValue placeholder="Post Type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            <SelectItem value="status">💭 Status</SelectItem>
            <SelectItem value="photo">📷 Photos</SelectItem>
            <SelectItem value="video">🎥 Videos</SelectItem>
            <SelectItem value="checkin">📍 Check-ins</SelectItem>
            <SelectItem value="life_event">🎉 Life Events</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      {/* Timeline content */}
      <TabsContent value="timeline" className="mt-0">
        <div className="relative">
          <AnimatePresence>
            {groupedPosts.map(([groupName, groupPosts]) => (
              <div key={groupName} className="mb-8">
                <div className="sticky top-4 z-10 mb-4">
                  <div className="inline-block bg-white px-4 py-2 rounded-full shadow-sm border border-gray-200">
                    <h2 className="font-semibold text-gray-800">{groupName}</h2>
                  </div>
                </div>
                
                {groupPosts.map(renderPost)}
              </div>
            ))}
          </AnimatePresence>
          
          {groupedPosts.length === 0 && (
            <div className="text-center py-12">
              <Calendar className="w-16 h-16 mx-auto mb-4 text-gray-300" />
              <h3 className="text-lg font-medium text-gray-600 mb-2">No posts found</h3>
              <p className="text-gray-500">Try adjusting your filters or check back later.</p>
            </div>
          )}
          
          {/* Load more button */}
          {onLoadMore && groupedPosts.length > 0 && (
            <div className="text-center mt-8">
              <Button 
                onClick={onLoadMore} 
                disabled={isLoading}
                variant="outline"
              >
                {isLoading ? 'Loading...' : 'Load More Posts'}
              </Button>
            </div>
          )}
        </div>
      </TabsContent>
      
      <TabsContent value="photos">
        <div className="grid grid-cols-3 gap-2">
          {posts
            .filter(post => post.images && post.images.length > 0)
            .flatMap(post => post.images!)
            .map((image, index) => (
              <OptimizedImage
                key={index}
                src={image}
                alt={`Photo ${index + 1}`}
                className="aspect-square object-cover rounded-lg cursor-pointer hover:opacity-80 transition-opacity"
              />
            ))}
        </div>
      </TabsContent>
      
      <TabsContent value="videos">
        <div className="grid grid-cols-2 gap-4">
          {posts
            .filter(post => post.video)
            .map(post => (
              <div key={post.id} className="relative">
                <video
                  controls
                  className="w-full aspect-video object-cover rounded-lg"
                  poster={post.images?.[0]}
                >
                  <source src={post.video} type="video/mp4" />
                </video>
                <div className="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                  {formatPostTime(post.createdAt)}
                </div>
              </div>
            ))}
        </div>
      </TabsContent>
    </div>
  );
};

export default Timeline;