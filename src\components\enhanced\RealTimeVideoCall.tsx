import React, { useState, useEffect, useRef, useCallback, memo } from 'react';
import { Video, VideoOff, Mic, MicOff, Phone, PhoneOff, Settings, Users, MessageCircle, Share2, Maximize, Minimize } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'sonner';

interface Participant {
  id: string;
  name: string;
  avatar: string;
  isVideoEnabled: boolean;
  isAudioEnabled: boolean;
  isScreenSharing: boolean;
  connectionQuality: 'excellent' | 'good' | 'poor';
}

interface RealTimeVideoCallProps {
  isOpen: boolean;
  onClose: () => void;
  participants: Participant[];
  currentUserId: string;
  isIncoming?: boolean;
  callerName?: string;
  callerAvatar?: string;
  onAccept?: () => void;
  onDecline?: () => void;
}

const RealTimeVideoCall: React.FC<RealTimeVideoCallProps> = memo(({
  isOpen,
  onClose,
  participants,
  currentUserId,
  isIncoming = false,
  callerName,
  callerAvatar,
  onAccept,
  onDecline
}) => {
  const [isVideoEnabled, setIsVideoEnabled] = useState(true);
  const [isAudioEnabled, setIsAudioEnabled] = useState(true);
  const [isScreenSharing, setIsScreenSharing] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showControls, setShowControls] = useState(true);
  const [callDuration, setCallDuration] = useState(0);
  const [isConnected, setIsConnected] = useState(!isIncoming);
  const [connectionQuality, setConnectionQuality] = useState<'excellent' | 'good' | 'poor'>('excellent');
  
  const localVideoRef = useRef<HTMLVideoElement>(null);
  const remoteVideoRef = useRef<HTMLVideoElement>(null);
  const controlsTimeoutRef = useRef<NodeJS.Timeout>();

  // Simulate call duration timer
  useEffect(() => {
    if (isConnected && isOpen) {
      const interval = setInterval(() => {
        setCallDuration(prev => prev + 1);
      }, 1000);
      return () => clearInterval(interval);
    }
  }, [isConnected, isOpen]);

  // Auto-hide controls after inactivity
  useEffect(() => {
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current);
    }
    
    if (showControls) {
      controlsTimeoutRef.current = setTimeout(() => {
        setShowControls(false);
      }, 3000);
    }

    return () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
    };
  }, [showControls]);

  // Simulate connection quality changes
  useEffect(() => {
    if (isConnected) {
      const interval = setInterval(() => {
        const qualities: ('excellent' | 'good' | 'poor')[] = ['excellent', 'good', 'poor'];
        const randomQuality = qualities[Math.floor(Math.random() * qualities.length)];
        setConnectionQuality(randomQuality);
      }, 10000);
      return () => clearInterval(interval);
    }
  }, [isConnected]);

  const handleToggleVideo = useCallback(() => {
    setIsVideoEnabled(prev => {
      const newState = !prev;
      toast.success(newState ? 'Camera turned on' : 'Camera turned off');
      return newState;
    });
  }, []);

  const handleToggleAudio = useCallback(() => {
    setIsAudioEnabled(prev => {
      const newState = !prev;
      toast.success(newState ? 'Microphone turned on' : 'Microphone turned off');
      return newState;
    });
  }, []);

  const handleToggleScreenShare = useCallback(() => {
    setIsScreenSharing(prev => {
      const newState = !prev;
      toast.success(newState ? 'Screen sharing started' : 'Screen sharing stopped');
      return newState;
    });
  }, []);

  const handleEndCall = useCallback(() => {
    toast.success('Call ended');
    onClose();
  }, [onClose]);

  const handleAcceptCall = useCallback(() => {
    setIsConnected(true);
    onAccept?.();
    toast.success('Call connected');
  }, [onAccept]);

  const handleDeclineCall = useCallback(() => {
    onDecline?.();
    onClose();
    toast.success('Call declined');
  }, [onDecline, onClose]);

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getConnectionQualityColor = (quality: string) => {
    switch (quality) {
      case 'excellent': return 'text-green-500';
      case 'good': return 'text-yellow-500';
      case 'poor': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  if (!isOpen) return null;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className={`fixed inset-0 bg-black z-50 flex flex-col ${
        isFullscreen ? 'p-0' : 'p-4'
      }`}
      onMouseMove={() => setShowControls(true)}
    >
      {/* Incoming Call Screen */}
      {isIncoming && !isConnected && (
        <div className="flex-1 flex items-center justify-center">
          <Card className="w-full max-w-md bg-white/10 backdrop-blur-lg border-white/20">
            <CardContent className="p-8 text-center">
              <motion.div
                animate={{ scale: [1, 1.1, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="mb-6"
              >
                <Avatar className="w-32 h-32 mx-auto">
                  <AvatarImage src={callerAvatar} />
                  <AvatarFallback className="text-2xl">
                    {callerName?.charAt(0) || 'U'}
                  </AvatarFallback>
                </Avatar>
              </motion.div>
              
              <h2 className="text-2xl font-bold text-white mb-2">
                {callerName || 'Unknown Caller'}
              </h2>
              <p className="text-white/70 mb-8">Incoming video call...</p>
              
              <div className="flex justify-center space-x-6">
                <Button
                  onClick={handleDeclineCall}
                  className="w-16 h-16 rounded-full bg-red-500 hover:bg-red-600 text-white"
                >
                  <PhoneOff className="w-6 h-6" />
                </Button>
                <Button
                  onClick={handleAcceptCall}
                  className="w-16 h-16 rounded-full bg-green-500 hover:bg-green-600 text-white"
                >
                  <Phone className="w-6 h-6" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Active Call Screen */}
      {isConnected && (
        <>
          {/* Video Grid */}
          <div className="flex-1 relative">
            {participants.length === 1 ? (
              // One-on-one call layout
              <div className="w-full h-full relative">
                <video
                  ref={remoteVideoRef}
                  className="w-full h-full object-cover"
                  autoPlay
                  playsInline
                />
                
                {/* Local video (picture-in-picture) */}
                <motion.div
                  drag
                  dragConstraints={{ left: 0, right: 300, top: 0, bottom: 200 }}
                  className="absolute top-4 right-4 w-48 h-36 bg-gray-900 rounded-lg overflow-hidden border-2 border-white/20 cursor-move"
                >
                  <video
                    ref={localVideoRef}
                    className="w-full h-full object-cover"
                    autoPlay
                    playsInline
                    muted
                  />
                  {!isVideoEnabled && (
                    <div className="absolute inset-0 bg-gray-800 flex items-center justify-center">
                      <Avatar className="w-16 h-16">
                        <AvatarFallback>You</AvatarFallback>
                      </Avatar>
                    </div>
                  )}
                </motion.div>
              </div>
            ) : (
              // Group call grid layout
              <div className="grid grid-cols-2 lg:grid-cols-3 gap-2 h-full p-4">
                {participants.map((participant) => (
                  <div
                    key={participant.id}
                    className="relative bg-gray-900 rounded-lg overflow-hidden"
                  >
                    <video
                      className="w-full h-full object-cover"
                      autoPlay
                      playsInline
                    />
                    
                    {!participant.isVideoEnabled && (
                      <div className="absolute inset-0 bg-gray-800 flex items-center justify-center">
                        <Avatar className="w-16 h-16">
                          <AvatarImage src={participant.avatar} />
                          <AvatarFallback>
                            {participant.name.charAt(0)}
                          </AvatarFallback>
                        </Avatar>
                      </div>
                    )}
                    
                    {/* Participant info */}
                    <div className="absolute bottom-2 left-2 right-2">
                      <div className="flex items-center justify-between">
                        <span className="text-white text-sm font-medium bg-black/50 px-2 py-1 rounded">
                          {participant.name}
                        </span>
                        <div className="flex items-center space-x-1">
                          {!participant.isAudioEnabled && (
                            <MicOff className="w-4 h-4 text-red-400" />
                          )}
                          {participant.isScreenSharing && (
                            <Share2 className="w-4 h-4 text-blue-400" />
                          )}
                          <div className={`w-2 h-2 rounded-full ${
                            participant.connectionQuality === 'excellent' ? 'bg-green-400' :
                            participant.connectionQuality === 'good' ? 'bg-yellow-400' : 'bg-red-400'
                          }`} />
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Call Controls */}
          <AnimatePresence>
            {showControls && (
              <motion.div
                initial={{ opacity: 0, y: 100 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 100 }}
                className="absolute bottom-0 left-0 right-0 p-6"
              >
                <div className="flex items-center justify-center space-x-4">
                  {/* Audio Toggle */}
                  <Button
                    onClick={handleToggleAudio}
                    className={`w-12 h-12 rounded-full ${
                      isAudioEnabled 
                        ? 'bg-gray-700 hover:bg-gray-600' 
                        : 'bg-red-500 hover:bg-red-600'
                    }`}
                  >
                    {isAudioEnabled ? (
                      <Mic className="w-5 h-5 text-white" />
                    ) : (
                      <MicOff className="w-5 h-5 text-white" />
                    )}
                  </Button>

                  {/* Video Toggle */}
                  <Button
                    onClick={handleToggleVideo}
                    className={`w-12 h-12 rounded-full ${
                      isVideoEnabled 
                        ? 'bg-gray-700 hover:bg-gray-600' 
                        : 'bg-red-500 hover:bg-red-600'
                    }`}
                  >
                    {isVideoEnabled ? (
                      <Video className="w-5 h-5 text-white" />
                    ) : (
                      <VideoOff className="w-5 h-5 text-white" />
                    )}
                  </Button>

                  {/* Screen Share */}
                  <Button
                    onClick={handleToggleScreenShare}
                    className={`w-12 h-12 rounded-full ${
                      isScreenSharing 
                        ? 'bg-blue-500 hover:bg-blue-600' 
                        : 'bg-gray-700 hover:bg-gray-600'
                    }`}
                  >
                    <Share2 className="w-5 h-5 text-white" />
                  </Button>

                  {/* End Call */}
                  <Button
                    onClick={handleEndCall}
                    className="w-12 h-12 rounded-full bg-red-500 hover:bg-red-600"
                  >
                    <PhoneOff className="w-5 h-5 text-white" />
                  </Button>

                  {/* Fullscreen Toggle */}
                  <Button
                    onClick={() => setIsFullscreen(!isFullscreen)}
                    className="w-12 h-12 rounded-full bg-gray-700 hover:bg-gray-600"
                  >
                    {isFullscreen ? (
                      <Minimize className="w-5 h-5 text-white" />
                    ) : (
                      <Maximize className="w-5 h-5 text-white" />
                    )}
                  </Button>

                  {/* Settings */}
                  <Button
                    className="w-12 h-12 rounded-full bg-gray-700 hover:bg-gray-600"
                  >
                    <Settings className="w-5 h-5 text-white" />
                  </Button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Call Info */}
          <AnimatePresence>
            {showControls && (
              <motion.div
                initial={{ opacity: 0, y: -50 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -50 }}
                className="absolute top-4 left-4 right-4"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Badge variant="secondary" className="bg-black/50 text-white">
                      {formatDuration(callDuration)}
                    </Badge>
                    <Badge 
                      variant="secondary" 
                      className={`bg-black/50 ${getConnectionQualityColor(connectionQuality)}`}
                    >
                      {connectionQuality}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-white hover:bg-white/20"
                    >
                      <MessageCircle className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-white hover:bg-white/20"
                    >
                      <Users className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </>
      )}
    </motion.div>
  );
});

RealTimeVideoCall.displayName = 'RealTimeVideoCall';

export default RealTimeVideoCall;
