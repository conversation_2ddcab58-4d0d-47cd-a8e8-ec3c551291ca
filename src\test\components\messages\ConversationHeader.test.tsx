import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { toast } from 'sonner';
import ConversationHeader from '@/components/messages/ConversationHeader';
import { MOCK_IMAGES } from '@/lib/constants';

// Mock sonner toast
vi.mock('sonner', () => ({
  toast: {
    info: vi.fn()
  }
}));

const mockUser = {
  name: '<PERSON>',
  avatar: MOCK_IMAGES.AVATARS[0],
  isOnline: true,
  lastActive: 'Active now'
};

const defaultProps = {
  user: mockUser,
  isMobile: false,
  onBackToList: vi.fn()
};

describe('ConversationHeader', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders user information', () => {
    render(<ConversationHeader {...defaultProps} />);
    
    expect(screen.getByText('<PERSON>')).toBeInTheDocument();
    expect(screen.getByText('Active now')).toBeInTheDocument();
  });

  it('shows back button on mobile', () => {
    render(<ConversationHeader {...defaultProps} isMobile={true} />);
    
    const backButton = screen.getByRole('button');
    expect(backButton).toHaveClass('md:hidden');
  });

  it('calls onBackToList when back button is clicked', () => {
    const onBackToList = vi.fn();
    render(<ConversationHeader {...defaultProps} isMobile={true} onBackToList={onBackToList} />);
    
    const backButton = screen.getByRole('button');
    fireEvent.click(backButton);
    
    expect(onBackToList).toHaveBeenCalled();
  });

  it('shows phone and video call buttons', () => {
    render(<ConversationHeader {...defaultProps} />);
    
    const buttons = screen.getAllByRole('button');
    expect(buttons).toHaveLength(3); // phone, video, more options
  });

  it('shows toast when phone button is clicked', () => {
    render(<ConversationHeader {...defaultProps} />);
    
    const phoneButton = screen.getAllByRole('button')[0];
    fireEvent.click(phoneButton);
    
    expect(toast.info).toHaveBeenCalledWith('Starting audio call');
  });

  it('shows toast when video button is clicked', () => {
    render(<ConversationHeader {...defaultProps} />);
    
    const videoButton = screen.getAllByRole('button')[1];
    fireEvent.click(videoButton);
    
    expect(toast.info).toHaveBeenCalledWith('Starting video call');
  });

  it('displays offline status when user is not online', () => {
    const offlineUser = {
      ...mockUser,
      isOnline: false,
      lastActive: '2 hours ago'
    };
    
    render(<ConversationHeader {...defaultProps} user={offlineUser} />);
    
    expect(screen.getByText('2 hours ago')).toBeInTheDocument();
    expect(screen.queryByText('Active now')).not.toBeInTheDocument();
  });

  it('hides back button on desktop', () => {
    render(<ConversationHeader {...defaultProps} isMobile={false} />);
    
    const buttons = screen.getAllByRole('button');
    // Should only have 3 buttons (phone, video, more) - no back button
    expect(buttons).toHaveLength(3);
  });
});
