import React, { useState, useEffect, useCallback } from 'react';
import {
  Bell,
  Heart,
  MessageCircle,
  UserPlus,
  Calendar,
  Tag,
  Share2,
  Gift,
  Briefcase,
  Users,
  Video,
  X,
  Check,
  Settings,
  Trash2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { formatDistanceToNow } from 'date-fns';
import { toast } from 'sonner';
import { motion, AnimatePresence } from 'framer-motion';
import NotificationService, { Notification, NotificationSettings } from '@/services/NotificationService';

type NotificationType = 
  | 'like' 
  | 'comment' 
  | 'share' 
  | 'friend_request' 
  | 'friend_accepted'
  | 'tag' 
  | 'event' 
  | 'birthday' 
  | 'job' 
  | 'group' 
  | 'video_call'
  | 'live_stream'
  | 'memory';

interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  timestamp: Date;
  isRead: boolean;
  isImportant: boolean;
  actionUrl?: string;
  actor: {
    id: string;
    name: string;
    avatar: string;
  };
  metadata?: {
    postId?: string;
    eventId?: string;
    groupId?: string;
    callId?: string;
  };
}

interface AdvancedNotificationCenterProps {
  isOpen: boolean;
  onClose: () => void;
}

const AdvancedNotificationCenter: React.FC<AdvancedNotificationCenterProps> = ({
  isOpen,
  onClose
}) => {
  const [activeTab, setActiveTab] = useState<'all' | 'unread' | 'important'>('all');
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [showSettings, setShowSettings] = useState(false);
  const [settings, setSettings] = useState<NotificationSettings | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  const notificationService = NotificationService.getInstance();

  // Initialize service and load data
  useEffect(() => {
    notificationService.init();
    loadNotifications();
    loadSettings();

    const handleNotificationAdded = (notification: Notification) => {
      setNotifications(prev => [notification, ...prev]);
      setUnreadCount(prev => prev + 1);
    };

    const handleNotificationRead = () => {
      loadNotifications();
    };

    const handleAllNotificationsRead = () => {
      setNotifications(prev => prev.map(n => ({ ...n, isRead: true })));
      setUnreadCount(0);
    };

    const handleNotificationDeleted = () => {
      loadNotifications();
    };

    const handleSettingsUpdated = (newSettings: NotificationSettings) => {
      setSettings(newSettings);
    };

    // Subscribe to events
    notificationService.on('notification_added', handleNotificationAdded);
    notificationService.on('notification_read', handleNotificationRead);
    notificationService.on('all_notifications_read', handleAllNotificationsRead);
    notificationService.on('notification_deleted', handleNotificationDeleted);
    notificationService.on('settings_updated', handleSettingsUpdated);

    return () => {
      notificationService.off('notification_added', handleNotificationAdded);
      notificationService.off('notification_read', handleNotificationRead);
      notificationService.off('all_notifications_read', handleAllNotificationsRead);
      notificationService.off('notification_deleted', handleNotificationDeleted);
      notificationService.off('settings_updated', handleSettingsUpdated);
    };
  }, [loadNotifications, loadSettings, notificationService]);

  const loadNotifications = useCallback(() => {
    const allNotifications = notificationService.getNotifications();
    setNotifications(allNotifications);
    setUnreadCount(notificationService.getUnreadCount());
  }, [notificationService]);

  const loadSettings = useCallback(() => {
    const currentSettings = notificationService.getSettings();
    setSettings(currentSettings);
  }, [notificationService]);

  const handleMarkAsRead = useCallback((notificationId: string) => {
    notificationService.markAsRead(notificationId);
  }, [notificationService]);

  const handleMarkAllAsRead = useCallback(() => {
    notificationService.markAllAsRead();
  }, [notificationService]);

  const handleDeleteNotification = useCallback((notificationId: string) => {
    notificationService.deleteNotification(notificationId);
  }, [notificationService]);

  const handleNotificationClick = useCallback((notification: Notification) => {
    if (!notification.isRead) {
      handleMarkAsRead(notification.id);
    }

    if (notification.actionUrl) {
      window.location.href = notification.actionUrl;
    }

    onClose();
  }, [handleMarkAsRead, onClose]);

  const handleUpdateSettings = useCallback((newSettings: Partial<NotificationSettings>) => {
    notificationService.updateSettings(newSettings);
  }, [notificationService]);

  const getNotificationIcon = (type: string) => {
    const iconMap = {
      like: Heart,
      comment: MessageCircle,
      share: Share2,
      friend_request: UserPlus,
      friend_accepted: UserPlus,
      tag: Tag,
      event: Calendar,
      birthday: Gift,
      job: Briefcase,
      group: Users,
      video_call: Video,
      live_stream: Video,
      memory: Heart
    };
    return iconMap[type] || Bell;
  };

  const getNotificationColor = (type: NotificationType) => {
    const colorMap = {
      like: 'text-red-500',
      comment: 'text-blue-500',
      share: 'text-green-500',
      friend_request: 'text-purple-500',
      friend_accepted: 'text-green-500',
      tag: 'text-yellow-500',
      event: 'text-indigo-500',
      birthday: 'text-pink-500',
      job: 'text-orange-500',
      group: 'text-teal-500',
      video_call: 'text-blue-600',
      live_stream: 'text-red-600',
      memory: 'text-purple-600'
    };
    return colorMap[type] || 'text-gray-500';
  };

  const filteredNotifications = notifications.filter(notification => {
    if (activeTab === 'unread' && notification.isRead) return false;
    if (activeTab === 'important' && !notification.isImportant) return false;
    if (searchQuery && !notification.title.toLowerCase().includes(searchQuery.toLowerCase()) &&
        !notification.message.toLowerCase().includes(searchQuery.toLowerCase())) return false;
    return true;
  });

  const importantCount = notifications.filter(n => n.isImportant && !n.isRead).length;

  if (!isOpen) return null;

  if (!isOpen) return null;

  return (
    <>
      <div className="fixed inset-0 bg-black/50 z-50 flex items-start justify-center pt-16">
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: -20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: -20 }}
          transition={{ duration: 0.2 }}
        >
          <Card className="w-full max-w-md mx-4 max-h-[80vh] overflow-hidden bg-white dark:bg-gray-800">
            {/* Header */}
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between mb-4">
                <CardTitle className="text-xl font-semibold flex items-center">
                  <Bell className="w-5 h-5 mr-2" />
                  Notifications
                  {unreadCount > 0 && (
                    <Badge variant="destructive" className="ml-2">
                      {unreadCount}
                    </Badge>
                  )}
                </CardTitle>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowSettings(true)}
                  >
                    <Settings className="w-4 h-4" />
                  </Button>
                  <Button variant="ghost" size="sm" onClick={onClose}>
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              {/* Search */}
              <div className="mb-4">
                <Input
                  placeholder="Search notifications..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full"
                />
              </div>

              {/* Tabs */}
              <Tabs value={activeTab} onValueChange={(value: 'all' | 'unread' | 'important') => setActiveTab(value)}>
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="all">
                    All
                    {notifications.length > 0 && (
                      <Badge variant="secondary" className="ml-2 text-xs">
                        {notifications.length}
                      </Badge>
                    )}
                  </TabsTrigger>
                  <TabsTrigger value="unread">
                    Unread
                    {unreadCount > 0 && (
                      <Badge variant="destructive" className="ml-2 text-xs">
                        {unreadCount}
                      </Badge>
                    )}
                  </TabsTrigger>
                  <TabsTrigger value="important">
                    Important
                    {importantCount > 0 && (
                      <Badge variant="default" className="ml-2 text-xs">
                        {importantCount}
                      </Badge>
                    )}
                  </TabsTrigger>
                </TabsList>
              </Tabs>

              {/* Actions */}
              <div className="flex items-center justify-between mt-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleMarkAllAsRead}
                  disabled={unreadCount === 0}
                >
                <Check className="w-4 h-4" />
                  Mark All Read
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => notificationService.clearAllNotifications()}
                  disabled={notifications.length === 0}
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Clear All
                </Button>
              </div>
            </CardHeader>

            {/* Notifications List */}
            <CardContent className="p-0">
              <div className="overflow-y-auto max-h-96">
                {filteredNotifications.length === 0 ? (
                  <div className="p-8 text-center text-gray-500">
                    <Bell className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                    <p>No notifications</p>
                    <p className="text-sm mt-1">You're all caught up!</p>
                  </div>
                ) : (
                  <AnimatePresence>
                    {filteredNotifications.map((notification) => {
                      const IconComponent = getNotificationIcon(notification.type);
                      const iconColor = getNotificationColor(notification.type);

                      return (
                        <motion.div
                          key={notification.id}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          exit={{ opacity: 0, x: 20 }}
                          transition={{ duration: 0.2 }}
                          className={`p-4 border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors ${
                            !notification.isRead ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                          }`}
                          onClick={() => handleNotificationClick(notification)}
                        >
                          <div className="flex items-start space-x-3">
                            {/* User Avatar or Icon */}
                            <div className="flex-shrink-0">
                            {notification.actor ? (
                                <Avatar className="w-10 h-10">
                                  <AvatarImage src={notification.actor.avatar} />
                                  <AvatarFallback>
                                    {notification.actor.name[0]}
                                  </AvatarFallback>
                                </Avatar>
                              ) : (
                                <div className={`w-10 h-10 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center ${iconColor}`}>
                                  <IconComponent className="w-5 h-5" />
                                </div>
                              )}
                            </div>

                            {/* Content */}
                            <div className="flex-1 min-w-0">
                              <div className="flex items-start justify-between">
                                <div className="flex-1">
                                  <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                    {notification.title}
                                  </p>
                                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                    {notification.message}
                                  </p>
                                  <div className="flex items-center space-x-2 mt-2">
                                    <span className="text-xs text-gray-500">
                                      {formatDistanceToNow(notification.timestamp, { addSuffix: true })}
                                    </span>
                                    {notification.isImportant && (
                                      <Badge variant="destructive" className="text-xs">
                                        Important
                                      </Badge>
                                    )}
                                    {!notification.isRead && (
                                      <div className="w-2 h-2 bg-blue-500 rounded-full" />
                                    )}
                                  </div>
                                </div>

                                {/* Actions */}
                                <div className="flex items-center space-x-1 ml-2">
                                  {!notification.isRead && (
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleMarkAsRead(notification.id);
                                      }}
                                      className="h-8 w-8 p-0"
                                    >
                                      <Check className="w-4 h-4" />
                                    </Button>
                                  )}
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleDeleteNotification(notification.id);
                                    }}
                                    className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
                                  >
                                    <X className="w-4 h-4" />
                                  </Button>
                                </div>
                              </div>

                              {/* Quick Actions for specific notification types */}
                              {notification.type === 'friend_request' && (
                                <div className="flex space-x-2 mt-3">
                                  <Button
                                    size="sm"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      toast.success('Friend request accepted');
                                      handleMarkAsRead(notification.id);
                                    }}
                                  >
                                    Accept
                                  </Button>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      toast.info('Friend request declined');
                                      handleDeleteNotification(notification.id);
                                    }}
                                  >
                                    Decline
                                  </Button>
                                </div>
                              )}
                            </div>
                          </div>
                        </motion.div>
                      );
                    })}
                  </AnimatePresence>
                )}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Settings Dialog */}
      {settings && (
        <Dialog open={showSettings} onOpenChange={setShowSettings}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Notification Settings</DialogTitle>
            </DialogHeader>

            <div className="space-y-6">
              {/* General Settings */}
              <div className="space-y-4">
                <h3 className="text-sm font-medium">General</h3>

                <div className="flex items-center justify-between">
                  <Label htmlFor="notifications-enabled">Enable Notifications</Label>
                  <Switch
                    id="notifications-enabled"
                    checked={settings.enabled}
                    onCheckedChange={(checked) =>
                      handleUpdateSettings({ enabled: checked })
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="sound-enabled">Sound</Label>
                  <Switch
                    id="sound-enabled"
                    checked={settings.sound}
                    onCheckedChange={(checked) =>
                      handleUpdateSettings({ sound: checked })
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="vibration-enabled">Vibration</Label>
                  <Switch
                    id="vibration-enabled"
                    checked={settings.vibration}
                    onCheckedChange={(checked) =>
                      handleUpdateSettings({ vibration: checked })
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="desktop-enabled">Desktop Notifications</Label>
                  <Switch
                    id="desktop-enabled"
                    checked={settings.desktop}
                    onCheckedChange={(checked) =>
                      handleUpdateSettings({ desktop: checked })
                    }
                  />
                </div>
              </div>

              {/* Categories */}
              <div className="space-y-4">
                <h3 className="text-sm font-medium">Categories</h3>

                <div className="flex items-center justify-between">
                  <Label htmlFor="social-enabled">Social</Label>
                  <Switch
                    id="social-enabled"
                    checked={settings.categories.social}
                    onCheckedChange={(checked) =>
                      handleUpdateSettings({
                        categories: { ...settings.categories, social: checked }
                      })
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="system-enabled">System</Label>
                  <Switch
                    id="system-enabled"
                    checked={settings.categories.system}
                    onCheckedChange={(checked) =>
                      handleUpdateSettings({
                        categories: { ...settings.categories, system: checked }
                      })
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="security-enabled">Security</Label>
                  <Switch
                    id="security-enabled"
                    checked={settings.categories.security}
                    onCheckedChange={(checked) =>
                      handleUpdateSettings({
                        categories: { ...settings.categories, security: checked }
                      })
                    }
                  />
                </div>
              </div>

              {/* Quiet Hours */}
              <div className="space-y-4">
                <h3 className="text-sm font-medium">Quiet Hours</h3>

                <div className="flex items-center justify-between">
                  <Label htmlFor="quiet-hours-enabled">Enable Quiet Hours</Label>
                  <Switch
                    id="quiet-hours-enabled"
                    checked={settings.quietHours.enabled}
                    onCheckedChange={(checked) =>
                      handleUpdateSettings({
                        quietHours: { ...settings.quietHours, enabled: checked }
                      })
                    }
                  />
                </div>

                {settings.quietHours.enabled && (
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="quiet-start">Start Time</Label>
                      <Input
                        id="quiet-start"
                        type="time"
                        value={settings.quietHours.start}
                        onChange={(e) =>
                          handleUpdateSettings({
                            quietHours: { ...settings.quietHours, start: e.target.value }
                          })
                        }
                      />
                    </div>
                    <div>
                      <Label htmlFor="quiet-end">End Time</Label>
                      <Input
                        id="quiet-end"
                        type="time"
                        value={settings.quietHours.end}
                        onChange={(e) =>
                          handleUpdateSettings({
                            quietHours: { ...settings.quietHours, end: e.target.value }
                          })
                        }
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
};

export default AdvancedNotificationCenter;
