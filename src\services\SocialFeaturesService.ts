import { EventEmitter } from '@/utils/EventEmitter';

// Enhanced interfaces for comprehensive social features
interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  coverPhoto?: string;
  bio?: string;
  location?: string;
  website?: string;
  birthDate?: Date;
  relationshipStatus?: 'single' | 'in_relationship' | 'married' | 'complicated';
  workInfo?: WorkInfo[];
  educationInfo?: EducationInfo[];
  isOnline: boolean;
  lastSeen: Date;
  mutualFriends?: User[];
  friendsCount: number;
  followersCount: number;
  followingCount: number;
  verification?: {
    isVerified: boolean;
    type: 'blue' | 'business' | 'creator';
  };
}

interface WorkInfo {
  company: string;
  position: string;
  startDate: Date;
  endDate?: Date;
  description?: string;
  isCurrentJob: boolean;
}

interface EducationInfo {
  institution: string;
  degree?: string;
  fieldOfStudy?: string;
  startYear: number;
  endYear?: number;
  description?: string;
}

interface Post {
  id: string;
  author: User;
  content: string;
  media?: MediaItem[];
  location?: Location;
  feeling?: string;
  tags?: User[];
  privacy: 'public' | 'friends' | 'friends_except' | 'specific_friends' | 'only_me';
  createdAt: Date;
  updatedAt: Date;
  reactions: Record<string, User[]>;
  comments: Comment[];
  shares: Share[];
  isLive?: boolean;
  liveViewers?: number;
  isPoll?: boolean;
  pollOptions?: PollOption[];
  isEvent?: boolean;
  eventInfo?: EventInfo;
  isMemory?: boolean;
  memoryYear?: number;
}

interface MediaItem {
  id: string;
  type: 'image' | 'video' | 'gif' | 'document';
  url: string;
  thumbnail?: string;
  duration?: number; // for videos
  dimensions?: { width: number; height: number };
  alt?: string;
  tags?: { x: number; y: number; user: User }[]; // photo tags
}

interface Comment {
  id: string;
  author: User;
  content: string;
  createdAt: Date;
  updatedAt: Date;
  reactions: Record<string, User[]>;
  replies: Comment[];
  parentCommentId?: string;
  media?: MediaItem[];
}

interface Share {
  id: string;
  user: User;
  comment?: string;
  createdAt: Date;
  privacy: string;
}

interface Location {
  id: string;
  name: string;
  address?: string;
  coordinates?: { lat: number; lng: number };
  placeType?: 'restaurant' | 'business' | 'landmark' | 'city' | 'other';
}

interface PollOption {
  id: string;
  text: string;
  votes: User[];
  percentage: number;
}

interface EventInfo {
  id: string;
  title: string;
  description: string;
  startDate: Date;
  endDate?: Date;
  location?: Location;
  isOnline: boolean;
  attendees: User[];
  interestedUsers: User[];
  organizer: User;
  coHosts?: User[];
  ticketPrice?: number;
  isPrivate: boolean;
}

interface Story {
  id: string;
  author: User;
  media: MediaItem;
  text?: string;
  backgroundColor?: string;
  music?: { title: string; artist: string; url: string };
  location?: Location;
  createdAt: Date;
  expiresAt: Date;
  viewers: User[];
  isHighlight?: boolean;
  highlightTitle?: string;
  reactions: Record<string, User[]>;
}

interface Group {
  id: string;
  name: string;
  description: string;
  coverPhoto?: string;
  privacy: 'public' | 'private' | 'secret';
  members: User[];
  admins: User[];
  moderators: User[];
  posts: Post[];
  rules?: string[];
  createdAt: Date;
  category: string;
  location?: Location;
  isVerified: boolean;
}

interface Page {
  id: string;
  name: string;
  username?: string;
  description: string;
  category: string;
  profilePhoto?: string;
  coverPhoto?: string;
  website?: string;
  phone?: string;
  email?: string;
  address?: string;
  hours?: Record<string, string>;
  followers: User[];
  admins: User[];
  posts: Post[];
  isVerified: boolean;
  rating?: number;
  reviews?: Review[];
  callToAction?: {
    text: string;
    url: string;
    type: 'book' | 'call' | 'message' | 'visit' | 'shop';
  };
}

interface Review {
  id: string;
  author: User;
  rating: number;
  content: string;
  createdAt: Date;
  photos?: string[];
  isRecommended: boolean;
}

interface Relationship {
  id: string;
  user1: User;
  user2: User;
  type: 'friend' | 'following' | 'blocked' | 'restricted';
  status: 'pending' | 'accepted' | 'declined';
  createdAt: Date;
  closeFriend?: boolean;
  customLists?: string[];
}

interface Activity {
  id: string;
  user: User;
  type: 'post_created' | 'comment_added' | 'reaction_added' | 'friend_added' | 'location_checked_in' | 'life_event' | 'photo_uploaded' | 'status_updated';
  target?: string; // post id, user id, etc.
  details: Record<string, unknown>;
  createdAt: Date;
  privacy: string;
}

interface LifeEvent {
  id: string;
  user: User;
  type: 'relationship' | 'work' | 'education' | 'family' | 'home' | 'health' | 'travel' | 'achievement';
  title: string;
  description?: string;
  date: Date;
  location?: Location;
  privacy: string;
  photos?: string[];
  tags?: User[];
}

interface Memory {
  id: string;
  user: User;
  type: 'post' | 'photo' | 'life_event' | 'friendship';
  content: Post | MediaItem | LifeEvent | Relationship;
  originalDate: Date;
  isPrivate: boolean;
  shared?: boolean;
  sharedAt?: Date;
}

interface Marketplace {
  listings: MarketplaceListing[];
  categories: MarketplaceCategory[];
  savedListings: string[];
  userListings: MarketplaceListing[];
}

interface MarketplaceListing {
  id: string;
  seller: User;
  title: string;
  description: string;
  price: number;
  currency: string;
  condition: 'new' | 'used' | 'refurbished';
  category: string;
  photos: string[];
  location: Location;
  isAvailable: boolean;
  createdAt: Date;
  updatedAt: Date;
  views: number;
  saves: number;
  isPromoted: boolean;
  deliveryOptions: string[];
  contactInfo: {
    phone?: string;
    email?: string;
    messenger: boolean;
  };
}

interface MarketplaceCategory {
  id: string;
  name: string;
  icon: string;
  subcategories: MarketplaceCategory[];
}

class SocialFeaturesService extends EventEmitter {
  private static instance: SocialFeaturesService;
  private currentUser: User | null = null;
  private posts: Map<string, Post> = new Map();
  private users: Map<string, User> = new Map();
  private relationships: Map<string, Relationship> = new Map();
  private groups: Map<string, Group> = new Map();
  private pages: Map<string, Page> = new Map();
  private stories: Map<string, Story> = new Map();
  private activities: Map<string, Activity> = new Map();
  private memories: Map<string, Memory> = new Map();
  private marketplace: Marketplace = {
    listings: [],
    categories: [],
    savedListings: [],
    userListings: []
  };

  static getInstance(): SocialFeaturesService {
    if (!SocialFeaturesService.instance) {
      SocialFeaturesService.instance = new SocialFeaturesService();
    }
    return SocialFeaturesService.instance;
  }

  constructor() {
    super();
    this.initializeMockData();
  }

  // User Management
  setCurrentUser(user: User): void {
    this.currentUser = user;
    this.users.set(user.id, user);
    this.emit('user-updated', user);
  }

  getCurrentUser(): User | null {
    return this.currentUser;
  }

  updateUserProfile(updates: Partial<User>): void {
    if (this.currentUser) {
      this.currentUser = { ...this.currentUser, ...updates };
      this.users.set(this.currentUser.id, this.currentUser);
      this.emit('profile-updated', this.currentUser);
    }
  }

  // Friend Management
  sendFriendRequest(userId: string): void {
    const targetUser = this.users.get(userId);
    if (!targetUser || !this.currentUser) return;

    const relationship: Relationship = {
      id: `rel-${Date.now()}`,
      user1: this.currentUser,
      user2: targetUser,
      type: 'friend',
      status: 'pending',
      createdAt: new Date()
    };

    this.relationships.set(relationship.id, relationship);
    this.emit('friend-request-sent', { relationship, targetUser });
  }

  acceptFriendRequest(relationshipId: string): void {
    const relationship = this.relationships.get(relationshipId);
    if (relationship) {
      relationship.status = 'accepted';
      this.relationships.set(relationshipId, relationship);
      this.emit('friend-request-accepted', relationship);
    }
  }

  declineFriendRequest(relationshipId: string): void {
    const relationship = this.relationships.get(relationshipId);
    if (relationship) {
      relationship.status = 'declined';
      this.relationships.set(relationshipId, relationship);
      this.emit('friend-request-declined', relationship);
    }
  }

  unfriend(userId: string): void {
    const relationshipToRemove = Array.from(this.relationships.values())
      .find(rel => 
        (rel.user1.id === this.currentUser?.id && rel.user2.id === userId) ||
        (rel.user2.id === this.currentUser?.id && rel.user1.id === userId)
      );

    if (relationshipToRemove) {
      this.relationships.delete(relationshipToRemove.id);
      this.emit('friendship-ended', relationshipToRemove);
    }
  }

  blockUser(userId: string): void {
    const targetUser = this.users.get(userId);
    if (!targetUser || !this.currentUser) return;

    const relationship: Relationship = {
      id: `block-${Date.now()}`,
      user1: this.currentUser,
      user2: targetUser,
      type: 'blocked',
      status: 'accepted',
      createdAt: new Date()
    };

    this.relationships.set(relationship.id, relationship);
    this.emit('user-blocked', relationship);
  }

  // Post Management
  createPost(postData: Partial<Post>): Post {
    if (!this.currentUser) throw new Error('User not authenticated');

    const post: Post = {
      id: `post-${Date.now()}`,
      author: this.currentUser,
      content: postData.content || '',
      media: postData.media || [],
      location: postData.location,
      feeling: postData.feeling,
      tags: postData.tags || [],
      privacy: postData.privacy || 'friends',
      createdAt: new Date(),
      updatedAt: new Date(),
      reactions: {},
      comments: [],
      shares: [],
      ...postData
    };

    this.posts.set(post.id, post);
    this.emit('post-created', post);
    
    // Create activity
    this.createActivity({
      type: 'post_created',
      target: post.id,
      details: { postContent: post.content.substring(0, 100) }
    });

    return post;
  }

  reactToPost(postId: string, reactionType: string): void {
    const post = this.posts.get(postId);
    if (!post || !this.currentUser) return;

    if (!post.reactions[reactionType]) {
      post.reactions[reactionType] = [];
    }

    // Remove existing reactions from this user
    Object.keys(post.reactions).forEach(type => {
      post.reactions[type] = post.reactions[type].filter(user => user.id !== this.currentUser!.id);
    });

    // Add new reaction
    post.reactions[reactionType].push(this.currentUser);
    this.posts.set(postId, post);
    this.emit('post-reaction-added', { post, reaction: reactionType, user: this.currentUser });
  }

  commentOnPost(postId: string, content: string, parentCommentId?: string): Comment {
    const post = this.posts.get(postId);
    if (!post || !this.currentUser) throw new Error('Post not found or user not authenticated');

    const comment: Comment = {
      id: `comment-${Date.now()}`,
      author: this.currentUser,
      content,
      createdAt: new Date(),
      updatedAt: new Date(),
      reactions: {},
      replies: [],
      parentCommentId
    };

    if (parentCommentId) {
      // Add as reply to existing comment
      const parentComment = post.comments.find(c => c.id === parentCommentId);
      if (parentComment) {
        parentComment.replies.push(comment);
      }
    } else {
      // Add as top-level comment
      post.comments.push(comment);
    }

    this.posts.set(postId, post);
    this.emit('comment-added', { post, comment });
    return comment;
  }

  sharePost(postId: string, comment?: string, privacy: string = 'friends'): Share {
    const post = this.posts.get(postId);
    if (!post || !this.currentUser) throw new Error('Post not found or user not authenticated');

    const share: Share = {
      id: `share-${Date.now()}`,
      user: this.currentUser,
      comment,
      createdAt: new Date(),
      privacy
    };

    post.shares.push(share);
    this.posts.set(postId, post);
    this.emit('post-shared', { post, share });
    return share;
  }

  // Story Management
  createStory(storyData: Partial<Story>): Story {
    if (!this.currentUser) throw new Error('User not authenticated');

    const story: Story = {
      id: `story-${Date.now()}`,
      author: this.currentUser,
      media: storyData.media!,
      text: storyData.text,
      backgroundColor: storyData.backgroundColor,
      music: storyData.music,
      location: storyData.location,
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
      viewers: [],
      reactions: {},
      ...storyData
    };

    this.stories.set(story.id, story);
    this.emit('story-created', story);
    return story;
  }

  viewStory(storyId: string): void {
    const story = this.stories.get(storyId);
    if (!story || !this.currentUser) return;

    if (!story.viewers.find(viewer => viewer.id === this.currentUser!.id)) {
      story.viewers.push(this.currentUser);
      this.stories.set(storyId, story);
      this.emit('story-viewed', { story, viewer: this.currentUser });
    }
  }

  // Group Management
  createGroup(groupData: Partial<Group>): Group {
    if (!this.currentUser) throw new Error('User not authenticated');

    const group: Group = {
      id: `group-${Date.now()}`,
      name: groupData.name!,
      description: groupData.description || '',
      privacy: groupData.privacy || 'public',
      members: [this.currentUser],
      admins: [this.currentUser],
      moderators: [],
      posts: [],
      createdAt: new Date(),
      category: groupData.category || 'General',
      isVerified: false,
      ...groupData
    };

    this.groups.set(group.id, group);
    this.emit('group-created', group);
    return group;
  }

  joinGroup(groupId: string): void {
    const group = this.groups.get(groupId);
    if (!group || !this.currentUser) return;

    if (!group.members.find(member => member.id === this.currentUser!.id)) {
      group.members.push(this.currentUser);
      this.groups.set(groupId, group);
      this.emit('group-joined', { group, user: this.currentUser });
    }
  }

  leaveGroup(groupId: string): void {
    const group = this.groups.get(groupId);
    if (!group || !this.currentUser) return;

    group.members = group.members.filter(member => member.id !== this.currentUser!.id);
    this.groups.set(groupId, group);
    this.emit('group-left', { group, user: this.currentUser });
  }

  // Page Management
  createPage(pageData: Partial<Page>): Page {
    if (!this.currentUser) throw new Error('User not authenticated');

    const page: Page = {
      id: `page-${Date.now()}`,
      name: pageData.name!,
      description: pageData.description || '',
      category: pageData.category || 'Business',
      followers: [],
      admins: [this.currentUser],
      posts: [],
      isVerified: false,
      ...pageData
    };

    this.pages.set(page.id, page);
    this.emit('page-created', page);
    return page;
  }

  followPage(pageId: string): void {
    const page = this.pages.get(pageId);
    if (!page || !this.currentUser) return;

    if (!page.followers.find(follower => follower.id === this.currentUser!.id)) {
      page.followers.push(this.currentUser);
      this.pages.set(pageId, page);
      this.emit('page-followed', { page, user: this.currentUser });
    }
  }

  // Activity Management
  createActivity(activityData: Partial<Activity>): Activity {
    if (!this.currentUser) throw new Error('User not authenticated');

    const activity: Activity = {
      id: `activity-${Date.now()}`,
      user: this.currentUser,
      type: activityData.type!,
      target: activityData.target,
      details: activityData.details || {},
      createdAt: new Date(),
      privacy: activityData.privacy || 'friends'
    };

    this.activities.set(activity.id, activity);
    this.emit('activity-created', activity);
    return activity;
  }

  // Memory Management
  createMemory(memoryData: Partial<Memory>): Memory {
    if (!this.currentUser) throw new Error('User not authenticated');

    const memory: Memory = {
      id: `memory-${Date.now()}`,
      user: this.currentUser,
      type: memoryData.type!,
      content: memoryData.content!,
      originalDate: memoryData.originalDate!,
      isPrivate: memoryData.isPrivate || false,
      ...memoryData
    };

    this.memories.set(memory.id, memory);
    this.emit('memory-created', memory);
    return memory;
  }

  shareMemory(memoryId: string): void {
    const memory = this.memories.get(memoryId);
    if (!memory) return;

    memory.shared = true;
    memory.sharedAt = new Date();
    this.memories.set(memoryId, memory);
    this.emit('memory-shared', memory);
  }

  // Marketplace Management
  createMarketplaceListing(listingData: Partial<MarketplaceListing>): MarketplaceListing {
    if (!this.currentUser) throw new Error('User not authenticated');

    const listing: MarketplaceListing = {
      id: `listing-${Date.now()}`,
      seller: this.currentUser,
      title: listingData.title!,
      description: listingData.description || '',
      price: listingData.price!,
      currency: listingData.currency || 'USD',
      condition: listingData.condition || 'used',
      category: listingData.category!,
      photos: listingData.photos || [],
      location: listingData.location!,
      isAvailable: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      views: 0,
      saves: 0,
      isPromoted: false,
      deliveryOptions: listingData.deliveryOptions || ['pickup'],
      contactInfo: {
        messenger: true,
        ...listingData.contactInfo
      }
    };

    this.marketplace.listings.push(listing);
    this.marketplace.userListings.push(listing);
    this.emit('marketplace-listing-created', listing);
    return listing;
  }

  saveMarketplaceListing(listingId: string): void {
    if (!this.marketplace.savedListings.includes(listingId)) {
      this.marketplace.savedListings.push(listingId);
      this.emit('marketplace-listing-saved', listingId);
    }
  }

  // Data Access Methods
  getPosts(): Post[] {
    return Array.from(this.posts.values()).sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }

  getUserPosts(userId: string): Post[] {
    return this.getPosts().filter(post => post.author.id === userId);
  }

  getFriends(): User[] {
    if (!this.currentUser) return [];
    
    return Array.from(this.relationships.values())
      .filter(rel => 
        rel.type === 'friend' && 
        rel.status === 'accepted' &&
        (rel.user1.id === this.currentUser!.id || rel.user2.id === this.currentUser!.id)
      )
      .map(rel => rel.user1.id === this.currentUser!.id ? rel.user2 : rel.user1);
  }

  getFriendRequests(): Relationship[] {
    if (!this.currentUser) return [];
    
    return Array.from(this.relationships.values())
      .filter(rel => 
        rel.type === 'friend' && 
        rel.status === 'pending' &&
        rel.user2.id === this.currentUser.id
      );
  }

  getStories(): Story[] {
    const now = new Date();
    return Array.from(this.stories.values())
      .filter(story => story.expiresAt > now)
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }

  getGroups(): Group[] {
    return Array.from(this.groups.values());
  }

  getUserGroups(): Group[] {
    if (!this.currentUser) return [];
    
    return this.getGroups().filter(group => 
      group.members.some(member => member.id === this.currentUser!.id)
    );
  }

  getPages(): Page[] {
    return Array.from(this.pages.values());
  }

  getActivities(): Activity[] {
    return Array.from(this.activities.values())
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }

  getMemories(): Memory[] {
    if (!this.currentUser) return [];
    
    return Array.from(this.memories.values())
      .filter(memory => memory.user.id === this.currentUser!.id)
      .sort((a, b) => b.originalDate.getTime() - a.originalDate.getTime());
  }

  getMarketplaceListings(): MarketplaceListing[] {
    return this.marketplace.listings.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }

  // Search and Discovery
  searchUsers(query: string): User[] {
    const lowercaseQuery = query.toLowerCase();
    return Array.from(this.users.values())
      .filter(user => 
        user.name.toLowerCase().includes(lowercaseQuery) ||
        user.email.toLowerCase().includes(lowercaseQuery)
      );
  }

  searchPosts(query: string): Post[] {
    const lowercaseQuery = query.toLowerCase();
    return this.getPosts()
      .filter(post => 
        post.content.toLowerCase().includes(lowercaseQuery) ||
        post.author.name.toLowerCase().includes(lowercaseQuery)
      );
  }

  searchGroups(query: string): Group[] {
    const lowercaseQuery = query.toLowerCase();
    return this.getGroups()
      .filter(group => 
        group.name.toLowerCase().includes(lowercaseQuery) ||
        group.description.toLowerCase().includes(lowercaseQuery)
      );
  }

  // Mock data initialization
  private initializeMockData(): void {
    // Initialize with some mock users, posts, etc.
    const mockUsers: User[] = [
      {
        id: 'user-1',
        name: 'Alice Johnson',
        email: '<EMAIL>',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=alice',
        isOnline: true,
        lastSeen: new Date(),
        friendsCount: 250,
        followersCount: 300,
        followingCount: 180,
        bio: 'Digital marketing specialist and travel enthusiast',
        location: 'New York, NY'
      },
      {
        id: 'user-2',
        name: 'Bob Smith',
        email: '<EMAIL>',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=bob',
        isOnline: false,
        lastSeen: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
        friendsCount: 180,
        followersCount: 200,
        followingCount: 220,
        bio: 'Software engineer and coffee lover',
        location: 'San Francisco, CA'
      }
    ];

    mockUsers.forEach(user => this.users.set(user.id, user));

    // Set first user as current user for demo
    this.setCurrentUser(mockUsers[0]);
  }

  // Cleanup
  destroy(): void {
    this.posts.clear();
    this.users.clear();
    this.relationships.clear();
    this.groups.clear();
    this.pages.clear();
    this.stories.clear();
    this.activities.clear();
    this.memories.clear();
    this.removeAllListeners();
  }
}

export default SocialFeaturesService;
export type {
  User,
  Post,
  Comment,
  Story,
  Group,
  Page,
  Relationship,
  Activity,
  Memory,
  MarketplaceListing,
  MediaItem,
  Location,
  LifeEvent
};
