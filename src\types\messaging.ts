// Shared messaging types to avoid duplication across components

export interface User {
  id: string;
  name: string;
  avatar: string;
  isOnline: boolean;
  lastActive: string;
  lastSeen?: Date;
}

export interface Message {
  id: string;
  conversationId: string;
  senderId: string;
  content: string;
  timestamp: Date;
  type: 'text' | 'image' | 'file' | 'audio' | 'video' | 'sticker' | 'gif';
  status: 'sending' | 'sent' | 'delivered' | 'read';
  replyTo?: string;
  reactions?: {
    emoji: string;
    userId: string;
    timestamp: Date;
  }[];
  attachments?: {
    id: string;
    type: 'image' | 'file' | 'audio' | 'video';
    url: string;
    name: string;
    size: number;
    thumbnail?: string;
  }[];
}

export interface Conversation {
  id: string;
  type: 'direct' | 'group';
  name?: string;
  user?: User; // For direct conversations
  participants?: User[]; // For group conversations
  lastMessage: {
    content: string;
    timestamp: string;
    isRead: boolean;
  };
  unreadCount: number;
  isTyping?: { [userId: string]: boolean };
  isMuted?: boolean;
  isPinned?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
  settings?: {
    theme?: string;
    nickname?: { [userId: string]: string };
    emoji?: string;
  };
}

export interface TypingIndicator {
  conversationId: string;
  userId: string;
  isTyping: boolean;
  timestamp: Date;
}

export interface FileAttachment {
  id: string;
  file: File;
  type: 'image' | 'file' | 'video' | 'audio';
  preview?: string;
  uploading?: boolean;
}

export interface MessageReaction {
  emoji: string;
  userId: string;
  timestamp: Date;
}