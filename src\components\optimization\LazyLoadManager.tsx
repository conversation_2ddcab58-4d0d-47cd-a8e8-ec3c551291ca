import React, { lazy, Suspense, memo, useCallback, useMemo } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { toast } from 'sonner';

// Lazy load components with better error handling and loading states
const LazyNewsFeed = lazy(() => 
  import('@/components/NewsFeed').catch(err => {
    console.error('Failed to load NewsFeed:', err);
    toast.error('Failed to load news feed');
    return { default: () => <div>Failed to load news feed</div> };
  })
);

const LazyStories = lazy(() => 
  import('@/components/Stories').catch(err => {
    console.error('Failed to load Stories:', err);
    return { default: () => <div>Failed to load stories</div> };
  })
);

const LazyMessaging = lazy(() => 
  import('@/components/messaging/RealTimeMessaging').catch(err => {
    console.error('Failed to load Messaging:', err);
    return { default: () => <div>Failed to load messaging</div> };
  })
);

const LazyVideoCall = lazy(() => 
  import('@/components/enhanced/RealTimeVideoCall').catch(err => {
    console.error('Failed to load VideoCall:', err);
    return { default: () => <div>Failed to load video call</div> };
  })
);

const LazyLiveStream = lazy(() => 
  import('@/components/enhanced/LiveStreamingStudio').catch(err => {
    console.error('Failed to load LiveStream:', err);
    return { default: () => <div>Failed to load live stream</div> };
  })
);

const LazyMarketplace = lazy(() => 
  import('@/components/FacebookMarketplace').catch(err => {
    console.error('Failed to load Marketplace:', err);
    return { default: () => <div>Failed to load marketplace</div> };
  })
);

const LazyWatch = lazy(() => 
  import('@/components/FacebookWatch').catch(err => {
    console.error('Failed to load Watch:', err);
    return { default: () => <div>Failed to load watch</div> };
  })
);

const LazyGroups = lazy(() => 
  import('@/components/groups/EnhancedGroupsManager').catch(err => {
    console.error('Failed to load Groups:', err);
    return { default: () => <div>Failed to load groups</div> };
  })
);

const LazyNotifications = lazy(() => 
  import('@/components/notifications/EnhancedNotificationSystem').catch(err => {
    console.error('Failed to load Notifications:', err);
    return { default: () => <div>Failed to load notifications</div> };
  })
);

// Enhanced loading fallbacks for different component types
const FeedLoadingFallback = memo(() => (
  <div className="space-y-4">
    {[...Array(3)].map((_, i) => (
      <div key={i} className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm animate-pulse">
        <div className="flex items-center space-x-3 mb-3">
          <div className="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
          <div className="flex-1">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-2"></div>
            <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/6"></div>
          </div>
        </div>
        <div className="space-y-2">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
        </div>
        <div className="h-48 bg-gray-200 dark:bg-gray-700 rounded mt-3"></div>
      </div>
    ))}
  </div>
));

const StoriesLoadingFallback = memo(() => (
  <div className="flex space-x-3 p-4">
    {[...Array(5)].map((_, i) => (
      <div key={i} className="flex-shrink-0">
        <div className="w-20 h-32 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"></div>
      </div>
    ))}
  </div>
));

const MessagingLoadingFallback = memo(() => (
  <div className="h-96 bg-white dark:bg-gray-800 rounded-lg p-4 animate-pulse">
    <div className="flex items-center space-x-3 mb-4">
      <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
    </div>
    <div className="space-y-3">
      {[...Array(4)].map((_, i) => (
        <div key={i} className="flex items-start space-x-3">
          <div className="w-6 h-6 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
          <div className="flex-1">
            <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
          </div>
        </div>
      ))}
    </div>
  </div>
));

const GenericLoadingFallback = memo(() => (
  <div className="flex items-center justify-center h-64">
    <LoadingSpinner size="lg" />
  </div>
));

// Error fallback component
const ErrorFallback = memo(({ error, resetErrorBoundary }: { error: Error; resetErrorBoundary: () => void }) => (
  <div className="flex flex-col items-center justify-center h-64 p-4 text-center">
    <div className="text-red-500 mb-4">
      <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
      </svg>
    </div>
    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
      Something went wrong
    </h3>
    <p className="text-gray-600 dark:text-gray-400 mb-4">
      {error.message || 'An unexpected error occurred'}
    </p>
    <button
      onClick={resetErrorBoundary}
      className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
    >
      Try again
    </button>
  </div>
));

// Component type definitions for better type safety
type LazyComponentType = 
  | 'newsfeed'
  | 'stories' 
  | 'messaging'
  | 'videocall'
  | 'livestream'
  | 'marketplace'
  | 'watch'
  | 'groups'
  | 'notifications';

interface LazyComponentProps {
  type: LazyComponentType;
  fallback?: React.ComponentType;
  [key: string]: any;
}

// Main lazy load manager component
const LazyLoadManager: React.FC<LazyComponentProps> = memo(({ 
  type, 
  fallback, 
  ...props 
}) => {
  // Memoize component selection to prevent unnecessary re-renders
  const { Component, LoadingFallback } = useMemo(() => {
    switch (type) {
      case 'newsfeed':
        return { Component: LazyNewsFeed, LoadingFallback: FeedLoadingFallback };
      case 'stories':
        return { Component: LazyStories, LoadingFallback: StoriesLoadingFallback };
      case 'messaging':
        return { Component: LazyMessaging, LoadingFallback: MessagingLoadingFallback };
      case 'videocall':
        return { Component: LazyVideoCall, LoadingFallback: GenericLoadingFallback };
      case 'livestream':
        return { Component: LazyLiveStream, LoadingFallback: GenericLoadingFallback };
      case 'marketplace':
        return { Component: LazyMarketplace, LoadingFallback: GenericLoadingFallback };
      case 'watch':
        return { Component: LazyWatch, LoadingFallback: GenericLoadingFallback };
      case 'groups':
        return { Component: LazyGroups, LoadingFallback: GenericLoadingFallback };
      case 'notifications':
        return { Component: LazyNotifications, LoadingFallback: GenericLoadingFallback };
      default:
        return { Component: () => <div>Unknown component type</div>, LoadingFallback: GenericLoadingFallback };
    }
  }, [type]);

  const FallbackComponent = fallback || LoadingFallback;

  const handleError = useCallback((error: Error, errorInfo: { componentStack: string }) => {
    console.error(`Error in lazy component ${type}:`, error, errorInfo);
    toast.error(`Failed to load ${type} component`);
  }, [type]);

  return (
    <ErrorBoundary
      FallbackComponent={ErrorFallback}
      onError={handleError}
      onReset={() => window.location.reload()}
    >
      <Suspense fallback={<FallbackComponent />}>
        <Component {...props} />
      </Suspense>
    </ErrorBoundary>
  );
});

LazyLoadManager.displayName = 'LazyLoadManager';

// Export individual lazy components for direct use
export {
  LazyNewsFeed,
  LazyStories,
  LazyMessaging,
  LazyVideoCall,
  LazyLiveStream,
  LazyMarketplace,
  LazyWatch,
  LazyGroups,
  LazyNotifications,
  FeedLoadingFallback,
  StoriesLoadingFallback,
  MessagingLoadingFallback,
  GenericLoadingFallback,
  ErrorFallback
};

export default LazyLoadManager;

// Preload utility for critical components
export const preloadComponent = (type: LazyComponentType) => {
  switch (type) {
    case 'newsfeed':
      return import('@/components/NewsFeed');
    case 'stories':
      return import('@/components/Stories');
    case 'messaging':
      return import('@/components/messaging/RealTimeMessaging');
    case 'videocall':
      return import('@/components/enhanced/RealTimeVideoCall');
    case 'livestream':
      return import('@/components/enhanced/LiveStreamingStudio');
    case 'marketplace':
      return import('@/components/FacebookMarketplace');
    case 'watch':
      return import('@/components/FacebookWatch');
    case 'groups':
      return import('@/components/groups/EnhancedGroupsManager');
    case 'notifications':
      return import('@/components/notifications/EnhancedNotificationSystem');
    default:
      return Promise.resolve();
  }
};

// Batch preload utility
export const preloadCriticalComponents = () => {
  // Preload the most commonly used components
  const criticalComponents: LazyComponentType[] = ['newsfeed', 'stories', 'messaging'];
  
  return Promise.allSettled(
    criticalComponents.map(component => preloadComponent(component))
  );
};

// Hook for intelligent preloading based on user behavior
export const useIntelligentPreload = () => {
  const preloadOnHover = useCallback((type: LazyComponentType) => {
    return () => preloadComponent(type);
  }, []);

  const preloadOnIdle = useCallback((types: LazyComponentType[]) => {
    if ('requestIdleCallback' in window) {
      window.requestIdleCallback(() => {
        types.forEach(type => preloadComponent(type));
      });
    } else {
      // Fallback for browsers without requestIdleCallback
      setTimeout(() => {
        types.forEach(type => preloadComponent(type));
      }, 100);
    }
  }, []);

  return { preloadOnHover, preloadOnIdle };
};
