import { useEffect } from 'react';
import { ThemeProvider as CustomThemeProvider } from '@/contexts/ThemeProvider';

export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Add meta theme-color for mobile browsers
  useEffect(() => {
    const metaThemeColor = document.querySelector('meta[name="theme-color"]');
    if (!metaThemeColor) {
      const meta = document.createElement('meta');
      meta.name = 'theme-color';
      meta.content = '#ffffff';
      document.head.appendChild(meta);
    }
    
    // Add viewport meta tag if not present
    const viewportMeta = document.querySelector('meta[name="viewport"]');
    if (!viewportMeta) {
      const meta = document.createElement('meta');
      meta.name = 'viewport';
      meta.content = 'width=device-width, initial-scale=1.0, viewport-fit=cover';
      document.head.appendChild(meta);
    }
  }, []);
  
  return <CustomThemeProvider>{children}</CustomThemeProvider>;
};

export default ThemeProvider;