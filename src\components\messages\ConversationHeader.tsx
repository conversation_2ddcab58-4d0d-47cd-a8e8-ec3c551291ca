import React from 'react';
import { MoreH<PERSON>zontal, Phone, Video } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';

import { User } from '@/types/messaging';

interface ConversationHeaderProps {
  user: User;
  isMobile: boolean;
  onBackToList: () => void;
}

const ConversationHeader: React.FC<ConversationHeaderProps> = ({
  user,
  isMobile,
  onBackToList
}) => {
  return (
    <div className="p-3 border-b flex items-center justify-between dark:border-gray-700">
      <div className="flex items-center space-x-3">
        {isMobile && (
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={onBackToList}
            className="md:hidden h-9 w-9 p-0"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="w-5 h-5"
            >
              <path d="m15 18-6-6 6-6" />
            </svg>
          </Button>
        )}
        <Avatar className="h-10 w-10">
          <AvatarImage src={user.avatar} />
          <AvatarFallback>{user.name.charAt(0)}</AvatarFallback>
        </Avatar>
        <div>
          <p className="font-medium text-sm dark:text-white">{user.name}</p>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            {user.isOnline ? 'Active now' : user.lastActive}
          </p>
        </div>
      </div>
      <div className="flex items-center space-x-1">
        <Button 
          variant="ghost" 
          size="sm" 
          className="h-9 w-9 p-0" 
          onClick={() => toast.info('Starting audio call')}
        >
          <Phone className="w-5 h-5" />
        </Button>
        <Button 
          variant="ghost" 
          size="sm" 
          className="h-9 w-9 p-0" 
          onClick={() => toast.info('Starting video call')}
        >
          <Video className="w-5 h-5" />
        </Button>
        <Button variant="ghost" size="sm" className="h-9 w-9 p-0">
          <MoreHorizontal className="w-5 h-5" />
        </Button>
      </div>
    </div>
  );
};

export default ConversationHeader;
