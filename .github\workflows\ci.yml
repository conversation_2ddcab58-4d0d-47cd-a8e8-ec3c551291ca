name: CI

on:
  push:
    branches:
      - main
      - refactor/lint-cleanup
      - refactor/lint-fix
  pull_request:
    branches:
      - main

jobs:
  build-lint-test:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.18.0'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run ESLint (fail on errors)
      run: npm run lint

    - name: Run unit tests
      run: npm run test:run

    - name: Build project
      run: npm run build

