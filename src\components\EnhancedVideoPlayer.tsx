import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  Play,
  Pause,
  Volume2,
  VolumeX,
  Maximize,
  Minimize,
  Settings,
  SkipBack,
  SkipForward,
  Users,
  MessageCircle,
  Share2,
  ThumbsUp,
  ThumbsDown,
  Bookmark,
  Subtitles
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import { motion, AnimatePresence } from 'framer-motion';
import VideoRecommendationService, { Video } from '@/services/VideoRecommendationService';

interface EnhancedVideoPlayerProps {
  video: Video;
  autoplay?: boolean;
  onVideoEnd?: () => void;
  onTimeUpdate?: (currentTime: number, duration: number) => void;
  className?: string;
}

const EnhancedVideoPlayer: React.FC<EnhancedVideoPlayerProps> = ({
  video,
  autoplay = false,
  onVideoEnd,
  onTimeUpdate,
  className = ''
}) => {
  const [isPlaying, setIsPlaying] = useState(autoplay);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState([80]);
  const [isMuted, setIsMuted] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showControls, setShowControls] = useState(true);
  const [quality, setQuality] = useState(video.quality);
  const [playbackSpeed, setPlaybackSpeed] = useState(1);
  const [showSettings, setShowSettings] = useState(false);
  const [showWatchParty, setShowWatchParty] = useState(false);
  const [showChat, setShowChat] = useState(false);
  const [chatMessage, setChatMessage] = useState('');
  const [chatMessages, setChatMessages] = useState<Array<{
    id: string;
    user: string;
    message: string;
    timestamp: Date;
  }>>([]);
  const [isLiked, setIsLiked] = useState(false);
  const [isDisliked, setIsDisliked] = useState(false);
  const [isSaved, setIsSaved] = useState(false);
  const [showSubtitles, setShowSubtitles] = useState(false);

  const videoRef = useRef<HTMLVideoElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const controlsTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const videoService = VideoRecommendationService.getInstance();

  // Initialize video
  useEffect(() => {
    const videoElement = videoRef.current;
    if (!videoElement) return;

    const handleLoadedMetadata = () => {
      setDuration(videoElement.duration);
    };

    const handleTimeUpdate = () => {
      const current = videoElement.currentTime;
      setCurrentTime(current);
      onTimeUpdate?.(current, videoElement.duration);
      
      // Record watch progress
      videoService.recordWatch(video.id, current, videoElement.duration);
    };

    const handleEnded = () => {
      setIsPlaying(false);
      onVideoEnd?.();
    };

    videoElement.addEventListener('loadedmetadata', handleLoadedMetadata);
    videoElement.addEventListener('timeupdate', handleTimeUpdate);
    videoElement.addEventListener('ended', handleEnded);

    return () => {
      videoElement.removeEventListener('loadedmetadata', handleLoadedMetadata);
      videoElement.removeEventListener('timeupdate', handleTimeUpdate);
      videoElement.removeEventListener('ended', handleEnded);
    };
  }, [video.id, onVideoEnd, onTimeUpdate, videoService]);

  // Auto-hide controls
  useEffect(() => {
    const resetControlsTimeout = () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
      setShowControls(true);
      controlsTimeoutRef.current = setTimeout(() => {
        if (isPlaying) {
          setShowControls(false);
        }
      }, 3000);
    };

    const handleMouseMove = () => resetControlsTimeout();
    const handleMouseLeave = () => {
      if (isPlaying) {
        setShowControls(false);
      }
    };

    const container = containerRef.current;
    if (container) {
      container.addEventListener('mousemove', handleMouseMove);
      container.addEventListener('mouseleave', handleMouseLeave);
    }

    return () => {
      if (container) {
        container.removeEventListener('mousemove', handleMouseMove);
        container.removeEventListener('mouseleave', handleMouseLeave);
      }
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
    };
  }, [isPlaying]);

  const togglePlay = useCallback(() => {
    const videoElement = videoRef.current;
    if (!videoElement) return;

    if (isPlaying) {
      videoElement.pause();
    } else {
      videoElement.play();
    }
    setIsPlaying(!isPlaying);
  }, [isPlaying]);

  const handleSeek = useCallback((value: number[]) => {
    const videoElement = videoRef.current;
    if (!videoElement) return;

    const newTime = (value[0] / 100) * duration;
    videoElement.currentTime = newTime;
    setCurrentTime(newTime);
  }, [duration]);

  const handleVolumeChange = useCallback((value: number[]) => {
    const videoElement = videoRef.current;
    if (!videoElement) return;

    const newVolume = value[0];
    videoElement.volume = newVolume / 100;
    setVolume(value);
    setIsMuted(newVolume === 0);
  }, []);

  const toggleMute = useCallback(() => {
    const videoElement = videoRef.current;
    if (!videoElement) return;

    if (isMuted) {
      videoElement.volume = volume[0] / 100;
      setIsMuted(false);
    } else {
      videoElement.volume = 0;
      setIsMuted(true);
    }
  }, [isMuted, volume]);

  const toggleFullscreen = useCallback(() => {
    const container = containerRef.current;
    if (!container) return;

    if (!document.fullscreenElement) {
      container.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  }, []);

  const skip = useCallback((seconds: number) => {
    const videoElement = videoRef.current;
    if (!videoElement) return;

    const newTime = Math.max(0, Math.min(duration, currentTime + seconds));
    videoElement.currentTime = newTime;
    setCurrentTime(newTime);
  }, [currentTime, duration]);

  const changePlaybackSpeed = useCallback((speed: number) => {
    const videoElement = videoRef.current;
    if (!videoElement) return;

    videoElement.playbackRate = speed;
    setPlaybackSpeed(speed);
  }, []);

  const handleLike = useCallback(() => {
    setIsLiked(!isLiked);
    if (isDisliked) setIsDisliked(false);
    videoService.recordInteraction(video.id, 'like', !isLiked);
    toast.success(isLiked ? 'Removed like' : 'Video liked');
  }, [isLiked, isDisliked, video.id, videoService]);

  const handleDislike = useCallback(() => {
    setIsDisliked(!isDisliked);
    if (isLiked) setIsLiked(false);
    toast.success(isDisliked ? 'Removed dislike' : 'Video disliked');
  }, [isDisliked, isLiked]);

  const handleSave = useCallback(() => {
    setIsSaved(!isSaved);
    toast.success(isSaved ? 'Removed from saved' : 'Video saved');
  }, [isSaved]);

  const handleShare = useCallback(() => {
    navigator.clipboard.writeText(window.location.href);
    videoService.recordInteraction(video.id, 'share');
    toast.success('Video link copied to clipboard');
  }, [video.id, videoService]);

  const sendChatMessage = useCallback(() => {
    if (chatMessage.trim()) {
      const newMessage = {
        id: `msg-${Date.now()}`,
        user: 'You',
        message: chatMessage.trim(),
        timestamp: new Date()
      };
      setChatMessages(prev => [...prev, newMessage]);
      setChatMessage('');
    }
  }, [chatMessage]);

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const formatViews = (views: number): string => {
    if (views >= 1000000) {
      return `${(views / 1000000).toFixed(1)}M`;
    } else if (views >= 1000) {
      return `${(views / 1000).toFixed(1)}K`;
    }
    return views.toString();
  };

  return (
    <div className={`relative bg-black rounded-lg overflow-hidden ${className}`}>
      <div ref={containerRef} className="relative w-full">
        {/* Video Element */}
        <video
          ref={videoRef}
          src={video.url}
          poster={video.thumbnail}
          className="w-full h-auto"
          onClick={togglePlay}
          onDoubleClick={toggleFullscreen}
        />

        {/* Live Badge */}
        {video.isLive && (
          <Badge className="absolute top-4 left-4 bg-red-500 text-white">
            LIVE
          </Badge>
        )}

        {/* Watch Party Badge */}
        {video.isWatchParty && (
          <Badge className="absolute top-4 right-4 bg-purple-500 text-white">
            Watch Party
          </Badge>
        )}

        {/* Controls Overlay */}
        <AnimatePresence>
          {showControls && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-black/40 flex flex-col justify-between p-4"
            >
              {/* Top Controls */}
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="text-white text-lg font-semibold">{video.title}</h3>
                  <div className="flex items-center space-x-2 text-white/80 text-sm">
                    <span>{formatViews(video.views)} views</span>
                    {video.isLive && <span>• {Math.floor(Math.random() * 1000)} watching</span>}
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  {video.isWatchParty && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowWatchParty(true)}
                      className="text-white hover:bg-white/20"
                    >
                      <Users className="w-4 h-4" />
                    </Button>
                  )}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowChat(!showChat)}
                    className="text-white hover:bg-white/20"
                  >
                    <MessageCircle className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowSettings(true)}
                    className="text-white hover:bg-white/20"
                  >
                    <Settings className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              {/* Center Play Button */}
              {!isPlaying && (
                <div className="flex justify-center items-center">
                  <Button
                    variant="ghost"
                    size="lg"
                    onClick={togglePlay}
                    className="w-16 h-16 rounded-full bg-white/20 hover:bg-white/30 text-white"
                  >
                    <Play className="w-8 h-8" />
                  </Button>
                </div>
              )}

              {/* Bottom Controls */}
              <div className="space-y-2">
                {/* Progress Bar */}
                <Slider
                  value={[duration > 0 ? (currentTime / duration) * 100 : 0]}
                  onValueChange={handleSeek}
                  max={100}
                  step={0.1}
                  className="w-full"
                />

                {/* Control Buttons */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={togglePlay}
                      className="text-white hover:bg-white/20"
                    >
                      {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                    </Button>

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => skip(-10)}
                      className="text-white hover:bg-white/20"
                    >
                      <SkipBack className="w-4 h-4" />
                    </Button>

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => skip(10)}
                      className="text-white hover:bg-white/20"
                    >
                      <SkipForward className="w-4 h-4" />
                    </Button>

                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={toggleMute}
                        className="text-white hover:bg-white/20"
                      >
                        {isMuted ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
                      </Button>
                      <Slider
                        value={isMuted ? [0] : volume}
                        onValueChange={handleVolumeChange}
                        max={100}
                        className="w-20"
                      />
                    </div>

                    <span className="text-white text-sm">
                      {formatTime(currentTime)} / {formatTime(duration)}
                    </span>
                  </div>

                  <div className="flex items-center space-x-2">
                    {video.subtitles && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setShowSubtitles(!showSubtitles)}
                        className={`text-white hover:bg-white/20 ${showSubtitles ? 'bg-white/20' : ''}`}
                      >
                        <Subtitles className="w-4 h-4" />
                      </Button>
                    )}

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={toggleFullscreen}
                      className="text-white hover:bg-white/20"
                    >
                      {isFullscreen ? <Minimize className="w-4 h-4" /> : <Maximize className="w-4 h-4" />}
                    </Button>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Chat Overlay */}
        {showChat && (
          <div className="absolute right-4 top-4 bottom-4 w-80 bg-black/80 rounded-lg p-4 flex flex-col">
            <h4 className="text-white font-semibold mb-2">Live Chat</h4>
            
            <div className="flex-1 overflow-y-auto space-y-2 mb-4">
              {chatMessages.map((msg) => (
                <div key={msg.id} className="text-white text-sm">
                  <span className="font-medium text-blue-400">{msg.user}:</span>{' '}
                  <span>{msg.message}</span>
                </div>
              ))}
            </div>

            <div className="flex space-x-2">
              <Input
                value={chatMessage}
                onChange={(e) => setChatMessage(e.target.value)}
                placeholder="Type a message..."
                className="flex-1 bg-white/20 border-white/30 text-white placeholder:text-white/60"
                onKeyPress={(e) => e.key === 'Enter' && sendChatMessage()}
              />
              <Button
                onClick={sendChatMessage}
                size="sm"
                className="bg-blue-500 hover:bg-blue-600"
              >
                Send
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Video Info and Actions */}
      <div className="p-4 bg-white dark:bg-gray-800">
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <h2 className="text-xl font-bold mb-2">{video.title}</h2>
            <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
              <span>{formatViews(video.views)} views</span>
              <span>•</span>
              <span>{video.uploadDate.toLocaleDateString()}</span>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant={isLiked ? "default" : "outline"}
              size="sm"
              onClick={handleLike}
              className="flex items-center space-x-1"
            >
              <ThumbsUp className="w-4 h-4" />
              <span>{formatViews(video.likes + (isLiked ? 1 : 0))}</span>
            </Button>

            <Button
              variant={isDisliked ? "default" : "outline"}
              size="sm"
              onClick={handleDislike}
              className="flex items-center space-x-1"
            >
              <ThumbsDown className="w-4 h-4" />
              <span>{formatViews(video.dislikes + (isDisliked ? 1 : 0))}</span>
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={handleShare}
              className="flex items-center space-x-1"
            >
              <Share2 className="w-4 h-4" />
              <span>Share</span>
            </Button>

            <Button
              variant={isSaved ? "default" : "outline"}
              size="sm"
              onClick={handleSave}
              className="flex items-center space-x-1"
            >
              <Bookmark className="w-4 h-4" />
              <span>Save</span>
            </Button>
          </div>
        </div>

        {/* Creator Info */}
        <div className="flex items-center space-x-3">
          <Avatar className="w-10 h-10">
            <AvatarImage src={video.creator.avatar} />
            <AvatarFallback>{video.creator.name[0]}</AvatarFallback>
          </Avatar>
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <h3 className="font-semibold">{video.creator.name}</h3>
              {video.creator.verified && (
                <Badge variant="secondary" className="text-xs">Verified</Badge>
              )}
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {formatViews(video.creator.subscribers)} subscribers
            </p>
          </div>
          <Button variant="default" size="sm">
            Subscribe
          </Button>
        </div>

        {/* Description */}
        <div className="mt-4">
          <p className="text-gray-700 dark:text-gray-300">{video.description}</p>
        </div>
      </div>

      {/* Settings Dialog */}
      <Dialog open={showSettings} onOpenChange={setShowSettings}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Video Settings</DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Quality</label>
              <select
                value={quality}
                onChange={(e) => setQuality(e.target.value as '360p' | '480p' | '720p' | '1080p' | '4K')}
                className="w-full mt-1 p-2 border rounded"
              >
                <option value="360p">360p</option>
                <option value="480p">480p</option>
                <option value="720p">720p</option>
                <option value="1080p">1080p</option>
                <option value="4K">4K</option>
              </select>
            </div>

            <div>
              <label className="text-sm font-medium">Playback Speed</label>
              <select
                value={playbackSpeed}
                onChange={(e) => changePlaybackSpeed(Number(e.target.value))}
                className="w-full mt-1 p-2 border rounded"
              >
                <option value={0.25}>0.25x</option>
                <option value={0.5}>0.5x</option>
                <option value={0.75}>0.75x</option>
                <option value={1}>Normal</option>
                <option value={1.25}>1.25x</option>
                <option value={1.5}>1.5x</option>
                <option value={2}>2x</option>
              </select>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default EnhancedVideoPlayer;
