import React, { useState, useRef, useEffect, useCallback } from 'react';
import { <PERSON>, VideoOff, Mic, MicOff, PhoneOff, Users, Maximize, Minimize, Settings } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { motion, AnimatePresence } from 'framer-motion';

interface Participant {
  id: string;
  name: string;
  avatar: string;
  isVideoOn: boolean;
  isAudioOn: boolean;
  isHost: boolean;
  isScreenSharing?: boolean;
}

interface VideoCallProps {
  participants: Participant[];
  currentUserId: string;
  onEndCall: () => void;
  onToggleVideo: () => void;
  onToggleMic: () => void;
  onInviteParticipants?: () => void;
  isGroupCall?: boolean;
}

const VideoCall: React.FC<VideoCallProps> = ({
  participants,
  currentUserId,
  onEndCall,
  onToggleVideo,
  onToggleMic,
  onInviteParticipants,
  isGroupCall = false
}) => {
  const [isVideoOn, setIsVideoOn] = useState(true);
  const [isAudioOn, setIsAudioOn] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showControls, setShowControls] = useState(true);
  const [callDuration, setCallDuration] = useState(0);
  const [showChat, setShowChat] = useState(false);
  const [chatMessages, setChatMessages] = useState<Array<{id: string, sender: string, message: string, timestamp: Date}>>([]);
  const [newMessage, setNewMessage] = useState('');
  const [dominantSpeaker, setDominantSpeaker] = useState<string | null>(null);
  
  const callStartTime = useRef(Date.now());
  const controlsTimeoutRef = useRef<NodeJS.Timeout>();

  const currentUser = participants.find(p => p.id === currentUserId);

  // Call duration timer
  useEffect(() => {
    const interval = setInterval(() => {
      setCallDuration(Math.floor((Date.now() - callStartTime.current) / 1000));
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Auto-hide controls
  useEffect(() => {
    const resetTimeout = () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
      controlsTimeoutRef.current = setTimeout(() => {
        setShowControls(false);
      }, 3000);
    };

    if (showControls) {
      resetTimeout();
    }

    return () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
    };
  }, [showControls]);

  // Simulate dominant speaker detection
  useEffect(() => {
    const interval = setInterval(() => {
      const speakingParticipants = participants.filter(p => p.isAudioOn);
      if (speakingParticipants.length > 0) {
        const randomSpeaker = speakingParticipants[Math.floor(Math.random() * speakingParticipants.length)];
        setDominantSpeaker(randomSpeaker.id);
      }
    }, 3000);

    return () => clearInterval(interval);
  }, [participants]);

  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleToggleVideo = useCallback(() => {
    setIsVideoOn(!isVideoOn);
    onToggleVideo();
    toast.success(isVideoOn ? 'Camera turned off' : 'Camera turned on');
  }, [isVideoOn, onToggleVideo]);

  const handleToggleMic = useCallback(() => {
    setIsAudioOn(!isAudioOn);
    onToggleMic();
    toast.success(isAudioOn ? 'Microphone muted' : 'Microphone unmuted');
  }, [isAudioOn, onToggleMic]);

  const handleEndCall = useCallback(() => {
    toast.success('Call ended');
    onEndCall();
  }, [onEndCall]);

  const handleSendMessage = useCallback(() => {
    if (!newMessage.trim()) return;
    
    const message = {
      id: Date.now().toString(),
      sender: currentUser?.name || 'You',
      message: newMessage,
      timestamp: new Date()
    };
    
    setChatMessages(prev => [...prev, message]);
    setNewMessage('');
  }, [newMessage, currentUser]);

  const toggleFullscreen = useCallback(() => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  }, []);

  const getGridLayout = () => {
    const totalParticipants = participants.length;
    if (totalParticipants <= 2) return 'grid-cols-1';
    if (totalParticipants <= 4) return 'grid-cols-2';
    if (totalParticipants <= 9) return 'grid-cols-3';
    return 'grid-cols-4';
  };

  return (
    <div 
      className={`relative bg-gray-900 text-white overflow-hidden ${
        isFullscreen ? 'fixed inset-0 z-50' : 'rounded-lg'
      }`}
      style={{ aspectRatio: isFullscreen ? 'auto' : '16/9' }}
      onMouseMove={() => setShowControls(true)}
    >
      {/* Main video grid */}
      <div className={`grid ${getGridLayout()} gap-2 h-full p-2`}>
        {participants.map((participant) => (
          <motion.div
            key={participant.id}
            className={`relative bg-gray-800 rounded-lg overflow-hidden ${
              dominantSpeaker === participant.id ? 'ring-2 ring-green-500' : ''
            }`}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
          >
            {participant.isVideoOn ? (
              <div className="w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                <span className="text-4xl font-bold opacity-50">
                  {participant.name.charAt(0)}
                </span>
              </div>
            ) : (
              <div className="w-full h-full bg-gray-700 flex items-center justify-center">
                <Avatar className="w-16 h-16">
                  <AvatarImage src={participant.avatar} />
                  <AvatarFallback className="text-2xl">
                    {participant.name.charAt(0)}
                  </AvatarFallback>
                </Avatar>
              </div>
            )}

            {/* Participant info overlay */}
            <div className="absolute bottom-2 left-2 flex items-center space-x-2">
              <Badge variant="secondary" className="bg-black/50 text-white">
                {participant.name}
                {participant.isHost && ' (Host)'}
              </Badge>
              
              <div className="flex space-x-1">
                {!participant.isAudioOn && (
                  <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                    <MicOff className="w-3 h-3" />
                  </div>
                )}
                {!participant.isVideoOn && (
                  <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                    <VideoOff className="w-3 h-3" />
                  </div>
                )}
                {participant.isScreenSharing && (
                  <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                    <Settings className="w-3 h-3" />
                  </div>
                )}
              </div>
            </div>

            {/* Speaking indicator */}
            {dominantSpeaker === participant.id && participant.isAudioOn && (
              <div className="absolute top-2 right-2">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse" />
              </div>
            )}
          </motion.div>
        ))}
      </div>

      {/* Call info */}
      <div className="absolute top-4 left-4">
        <Badge variant="secondary" className="bg-black/50 text-white">
          {formatDuration(callDuration)}
        </Badge>
      </div>

      {/* Participant count */}
      {isGroupCall && (
        <div className="absolute top-4 right-4">
          <Badge variant="secondary" className="bg-black/50 text-white flex items-center space-x-1">
            <Users className="w-4 h-4" />
            <span>{participants.length}</span>
          </Badge>
        </div>
      )}

      {/* Controls */}
      <AnimatePresence>
        {showControls && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            className="absolute bottom-4 left-1/2 transform -translate-x-1/2"
          >
            <Card className="bg-black/50 border-gray-600">
              <CardContent className="p-3">
                <div className="flex items-center space-x-3">
                  {/* Video toggle */}
                  <Button
                    variant={isVideoOn ? "secondary" : "destructive"}
                    size="sm"
                    className="w-12 h-12 rounded-full"
                    onClick={handleToggleVideo}
                  >
                    {isVideoOn ? <Video className="w-5 h-5" /> : <VideoOff className="w-5 h-5" />}
                  </Button>

                  {/* Audio toggle */}
                  <Button
                    variant={isAudioOn ? "secondary" : "destructive"}
                    size="sm"
                    className="w-12 h-12 rounded-full"
                    onClick={handleToggleMic}
                  >
                    {isAudioOn ? <Mic className="w-5 h-5" /> : <MicOff className="w-5 h-5" />}
                  </Button>

                  {/* End call */}
                  <Button
                    variant="destructive"
                    size="sm"
                    className="w-12 h-12 rounded-full"
                    onClick={handleEndCall}
                  >
                    <PhoneOff className="w-5 h-5" />
                  </Button>

                  {/* Chat toggle */}
                  <Button
                    variant="secondary"
                    size="sm"
                    className="w-12 h-12 rounded-full relative"
                    onClick={() => setShowChat(!showChat)}
                  >
                    <MessageCircle className="w-5 h-5" />
                    {chatMessages.length > 0 && (
                      <Badge className="absolute -top-1 -right-1 w-5 h-5 p-0 text-xs">
                        {chatMessages.length}
                      </Badge>
                    )}
                  </Button>

                  {/* Invite participants */}
                  {isGroupCall && onInviteParticipants && (
                    <Button
                      variant="secondary"
                      size="sm"
                      className="w-12 h-12 rounded-full"
                      onClick={onInviteParticipants}
                    >
                      <Users className="w-5 h-5" />
                    </Button>
                  )}

                  {/* Fullscreen toggle */}
                  <Button
                    variant="secondary"
                    size="sm"
                    className="w-12 h-12 rounded-full"
                    onClick={toggleFullscreen}
                  >
                    {isFullscreen ? <Minimize className="w-5 h-5" /> : <Maximize className="w-5 h-5" />}
                  </Button>

                  {/* More options */}
                  <Button
                    variant="secondary"
                    size="sm"
                    className="w-12 h-12 rounded-full"
                  >
                    <MoreHorizontal className="w-5 h-5" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Chat sidebar */}
      <AnimatePresence>
        {showChat && (
          <motion.div
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            className="absolute right-0 top-0 h-full w-80 bg-white text-black border-l border-gray-300"
          >
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold">Chat</h3>
                <Button variant="ghost" size="sm" onClick={() => setShowChat(false)}>
                  ×
                </Button>
              </div>
            </div>
            
            <div className="flex-1 overflow-y-auto p-4 space-y-3" style={{ height: 'calc(100% - 120px)' }}>
              {chatMessages.map((msg) => (
                <div key={msg.id} className="text-sm">
                  <div className="font-medium text-blue-600">{msg.sender}</div>
                  <div className="text-gray-800">{msg.message}</div>
                  <div className="text-xs text-gray-500">
                    {msg.timestamp.toLocaleTimeString()}
                  </div>
                </div>
              ))}
            </div>
            
            <div className="p-4 border-t border-gray-200">
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                  placeholder="Type a message..."
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg text-sm"
                />
                <Button size="sm" onClick={handleSendMessage}>
                  Send
                </Button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default VideoCall;