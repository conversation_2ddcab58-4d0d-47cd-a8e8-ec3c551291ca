import React from 'react';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Copy, Check, UserPlus } from 'lucide-react';
import { toast } from 'sonner';
import { getSafeImage } from '@/lib/constants';

interface Friend {
  id: string;
  name: string;
  avatar: string;
  isOnline: boolean;
}

interface InviteFriendsModalProps {
  isOpen: boolean;
  onClose: () => void;
  sessionId: string;
  inviteLink: string;
  linkCopied: boolean;
  onCopyInvite: () => void;
  friends: Friend[];
}

const InviteFriendsModal: React.FC<InviteFriendsModalProps> = ({
  isOpen,
  onClose,
  sessionId,
  inviteLink,
  linkCopied,
  onCopyInvite,
  friends
}) => {
  const handleInviteFriend = (friend: Friend) => {
    toast.success(`Invitation sent to ${friend.name}`);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <UserPlus className="w-5 h-5 mr-2" />
            Invite Friends
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Session Info */}
          <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
              Session ID
            </p>
            <p className="font-mono text-lg font-semibold dark:text-white">
              {sessionId}
            </p>
          </div>
          
          {/* Share Link */}
          <div className="space-y-2">
            <label className="text-sm font-medium dark:text-gray-200">
              Share Link
            </label>
            <div className="flex space-x-2">
              <Input
                value={inviteLink}
                readOnly
                className="flex-1 font-mono text-sm dark:bg-gray-700 dark:border-gray-600"
                aria-label="Invitation link"
              />
              <Button
                onClick={onCopyInvite}
                variant="outline"
                size="sm"
                className="px-3"
                aria-label={linkCopied ? 'Link copied' : 'Copy link'}
              >
                {linkCopied ? (
                  <Check className="w-4 h-4 text-green-600" />
                ) : (
                  <Copy className="w-4 h-4" />
                )}
              </Button>
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Anyone with this link can join your watch party
            </p>
          </div>
          
          {/* Friends List */}
          <div className="space-y-2">
            <label className="text-sm font-medium dark:text-gray-200">
              Invite Friends
            </label>
            <div className="max-h-48 overflow-y-auto space-y-2">
              {friends.map((friend) => (
                <div 
                  key={friend.id} 
                  className="flex items-center justify-between p-2 rounded-lg border dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <div className="relative">
                      <Avatar className="w-8 h-8">
                        <AvatarImage 
                          src={getSafeImage(friend.avatar)} 
                          alt={friend.name} 
                        />
                        <AvatarFallback className="text-xs">
                          {friend.name.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      {friend.isOnline && (
                        <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 rounded-full border-2 border-white dark:border-gray-800" />
                      )}
                    </div>
                    <div>
                      <p className="font-medium text-sm dark:text-gray-200">
                        {friend.name}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {friend.isOnline ? 'Online' : 'Offline'}
                      </p>
                    </div>
                  </div>
                  <Button
                    onClick={() => handleInviteFriend(friend)}
                    variant="outline"
                    size="sm"
                    className="text-xs"
                    aria-label={`Invite ${friend.name}`}
                  >
                    Invite
                  </Button>
                </div>
              ))}
              
              {friends.length === 0 && (
                <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                  <UserPlus className="w-8 h-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">No friends available to invite</p>
                </div>
              )}
            </div>
          </div>
          
          {/* Actions */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default InviteFriendsModal;