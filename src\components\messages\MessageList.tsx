import React from 'react';
import { Search, Edit, Filter } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import OnlineStatus from '../OnlineStatus';

import { Conversation } from '@/types/messaging';

interface MessageListProps {
  conversations: Conversation[];
  selectedConversationId: string | null;
  searchQuery: string;
  onSearchChange: (query: string) => void;
  onSelectConversation: (id: string) => void;
  isMobile: boolean;
  showConversation: boolean;
}

const MessageList: React.FC<MessageListProps> = ({
  conversations,
  selectedConversationId,
  searchQuery,
  onSearchChange,
  onSelectConversation,
  isMobile,
  showConversation
}) => {
  // Only show if not mobile or not viewing a conversation
  if (isMobile && showConversation) return null;

  return (
    <div className="w-full md:w-80 border-r dark:border-gray-700 flex flex-col">
      <div className="p-3 border-b dark:border-gray-700">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-xl font-bold dark:text-white">Messages</h2>
          <div className="flex space-x-1">
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <Edit className="w-5 h-5 text-gray-500 dark:text-gray-400" />
            </Button>
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <Filter className="w-5 h-5 text-gray-500 dark:text-gray-400" />
            </Button>
          </div>
        </div>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Search messages"
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-10 dark:bg-gray-700 dark:border-gray-600 dark:text-white h-9"
          />
        </div>
      </div>
      <div className="flex-1 overflow-y-auto">
        {conversations
          .filter(conversation => 
            conversation.user.name.toLowerCase().includes(searchQuery.toLowerCase())
          )
          .map((conversation) => (
          <div
            key={conversation.id}
            className={`p-3 hover:bg-gray-50 cursor-pointer flex items-center space-x-3 dark:hover:bg-gray-700 ${
              selectedConversationId === conversation.id ? 'bg-blue-50 dark:bg-blue-900/20' : ''
            } ${conversation.unreadCount > 0 ? 'bg-blue-50 dark:bg-blue-900/20' : ''}`}
            onClick={() => onSelectConversation(conversation.id)}
          >
            <div className="relative">
              <Avatar className="h-12 w-12">
                <AvatarImage src={conversation.user.avatar} />
                <AvatarFallback>{conversation.user.name.charAt(0)}</AvatarFallback>
              </Avatar>
              <OnlineStatus isOnline={conversation.user.isOnline} />
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <p className="font-medium text-sm text-gray-900 truncate dark:text-white">{conversation.user.name}</p>
                <p className="text-xs text-gray-500 dark:text-gray-400">{conversation.lastMessage?.timestamp}</p>
              </div>
              <div className="flex items-center justify-between">
                <p className={`text-sm truncate ${
                  conversation.unreadCount ? 'text-gray-900 font-medium dark:text-white' : 'text-gray-500 dark:text-gray-400'
                }`}>
                  {conversation.lastMessage?.content}
                </p>
                {conversation.unreadCount ? (
                  <Badge className="ml-2 bg-blue-600">{conversation.unreadCount}</Badge>
                ) : null}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default MessageList;
