import React, { useState, useEffect, useCallback } from 'react';
import { MapPin, Search, Clock, Users, Star, Navigation } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { toast } from 'sonner';
import { motion, AnimatePresence } from 'framer-motion';

interface Location {
  id: string;
  name: string;
  address: string;
  category: string;
  distance: number;
  rating: number;
  checkinsCount: number;
  recentVisitors: Array<{
    id: string;
    name: string;
    avatar: string;
    checkedInAt: Date;
  }>;
  coordinates: {
    lat: number;
    lng: number;
  };
}

interface CheckInProps {
  isOpen: boolean;
  onClose: () => void;
  onCheckIn: (location: Location, message?: string) => void;
}

const CheckIn: React.FC<CheckInProps> = ({ isOpen, onClose, onCheckIn }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(null);
  const [checkInMessage, setCheckInMessage] = useState('');
  const [nearbyLocations, setNearbyLocations] = useState<Location[]>([]);
  const [isLoadingLocation, setIsLoadingLocation] = useState(false);
  const [userLocation, setUserLocation] = useState<{ lat: number; lng: number } | null>(null);

  // Mock nearby locations data
  const mockLocations: Location[] = [
    {
      id: '1',
      name: 'Central Park',
      address: '59th St to 110th St, New York, NY',
      category: 'Park',
      distance: 0.2,
      rating: 4.8,
      checkinsCount: 15420,
      recentVisitors: [
        {
          id: '1',
          name: 'John Doe',
          avatar: 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?w=400&h=400&fit=crop&crop=face',
          checkedInAt: new Date(Date.now() - 30 * 60 * 1000)
        },
        {
          id: '2',
          name: 'Jane Smith',
          avatar: 'https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg?w=400&h=400&fit=crop&crop=face',
          checkedInAt: new Date(Date.now() - 45 * 60 * 1000)
        }
      ],
      coordinates: { lat: 40.7829, lng: -73.9654 }
    },
    {
      id: '2',
      name: 'Starbucks Coffee',
      address: '1585 Broadway, New York, NY',
      category: 'Coffee Shop',
      distance: 0.1,
      rating: 4.2,
      checkinsCount: 8934,
      recentVisitors: [
        {
          id: '3',
          name: 'Mike Johnson',
          avatar: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?w=400&h=400&fit=crop&crop=face',
          checkedInAt: new Date(Date.now() - 15 * 60 * 1000)
        }
      ],
      coordinates: { lat: 40.7589, lng: -73.9851 }
    },
    {
      id: '3',
      name: 'Times Square',
      address: 'Manhattan, New York, NY',
      category: 'Tourist Attraction',
      distance: 0.3,
      rating: 4.5,
      checkinsCount: 45678,
      recentVisators: [],
      coordinates: { lat: 40.7580, lng: -73.9855 }
    },
    {
      id: '4',
      name: 'The Metropolitan Museum',
      address: '1000 5th Ave, New York, NY',
      category: 'Museum',
      distance: 0.5,
      rating: 4.9,
      checkinsCount: 23456,
      recentVisitors: [
        {
          id: '4',
          name: 'Sarah Wilson',
          avatar: 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?w=400&h=400&fit=crop&crop=face',
          checkedInAt: new Date(Date.now() - 2 * 60 * 60 * 1000)
        }
      ],
      coordinates: { lat: 40.7794, lng: -73.9632 }
    }
  ];

  // Get user's current location
  useEffect(() => {
    if (isOpen && !userLocation) {
      setIsLoadingLocation(true);
      
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            setUserLocation({
              lat: position.coords.latitude,
              lng: position.coords.longitude
            });
            setIsLoadingLocation(false);
          },
          (error) => {
            console.error('Error getting location:', error);
            setIsLoadingLocation(false);
            toast.error('Unable to get your location');
          }
        );
      } else {
        setIsLoadingLocation(false);
        toast.error('Geolocation is not supported');
      }
    }
  }, [isOpen, userLocation]);

  // Filter locations based on search query
  const filteredLocations = mockLocations.filter(location =>
    location.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    location.address.toLowerCase().includes(searchQuery.toLowerCase()) ||
    location.category.toLowerCase().includes(searchQuery.toLowerCase())
  );

  useEffect(() => {
    setNearbyLocations(filteredLocations);
  }, [searchQuery, filteredLocations]);

  const handleLocationSelect = useCallback((location: Location) => {
    setSelectedLocation(location);
  }, []);

  const handleCheckIn = useCallback(() => {
    if (!selectedLocation) return;
    
    onCheckIn(selectedLocation, checkInMessage);
    setSelectedLocation(null);
    setCheckInMessage('');
    setSearchQuery('');
    onClose();
    toast.success(`Checked in at ${selectedLocation.name}`);
  }, [selectedLocation, checkInMessage, onCheckIn, onClose]);

  const formatDistance = (distance: number): string => {
    if (distance < 1) {
      return `${Math.round(distance * 1000)}m`;
    }
    return `${distance.toFixed(1)}km`;
  };

  const formatTimeAgo = (date: Date): string => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`;
    }
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    }
    
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <MapPin className="w-5 h-5 text-blue-500" />
            <span>Check In</span>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <Input
              placeholder="Search for a place..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Current location button */}
          <Button
            variant="outline"
            className="w-full justify-start"
            disabled={isLoadingLocation}
            onClick={() => {
              // Handle current location check-in
              toast.info('Getting your current location...');
            }}
          >
            <Navigation className="w-4 h-4 mr-2" />
            {isLoadingLocation ? 'Getting location...' : 'Use current location'}
          </Button>

          {/* Nearby locations */}
          <div className="space-y-2 max-h-96 overflow-y-auto">
            <h3 className="font-medium text-sm text-gray-600">Nearby places</h3>
            
            <AnimatePresence>
              {nearbyLocations.map((location) => (
                <motion.div
                  key={location.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.2 }}
                >
                  <Card 
                    className={`cursor-pointer transition-all hover:shadow-md ${
                      selectedLocation?.id === location.id ? 'ring-2 ring-blue-500 bg-blue-50' : ''
                    }`}
                    onClick={() => handleLocationSelect(location)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <h4 className="font-medium">{location.name}</h4>
                            <Badge variant="secondary" className="text-xs">
                              {location.category}
                            </Badge>
                          </div>
                          
                          <p className="text-sm text-gray-600 mb-2">{location.address}</p>
                          
                          <div className="flex items-center space-x-4 text-xs text-gray-500">
                            <div className="flex items-center space-x-1">
                              <MapPin className="w-3 h-3" />
                              <span>{formatDistance(location.distance)}</span>
                            </div>
                            
                            <div className="flex items-center space-x-1">
                              <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                              <span>{location.rating}</span>
                            </div>
                            
                            <div className="flex items-center space-x-1">
                              <Users className="w-3 h-3" />
                              <span>{location.checkinsCount.toLocaleString()} check-ins</span>
                            </div>
                          </div>
                          
                          {/* Recent visitors */}
                          {location.recentVisitors && location.recentVisitors.length > 0 && (
                            <div className="mt-3">
                              <div className="flex items-center space-x-2">
                                <div className="flex -space-x-2">
                                  {location.recentVisitors.slice(0, 3).map((visitor) => (
                                    <Avatar key={visitor.id} className="w-6 h-6 border-2 border-white">
                                      <AvatarImage src={visitor.avatar} />
                                      <AvatarFallback className="text-xs">
                                        {visitor.name.charAt(0)}
                                      </AvatarFallback>
                                    </Avatar>
                                  ))}
                                </div>
                                <span className="text-xs text-gray-500">
                                  {location.recentVisitors[0].name} and {location.recentVisitors.length - 1} others checked in recently
                                </span>
                              </div>
                            </div>
                          )}
                        </div>
                        
                        <div className="text-right">
                          <Clock className="w-4 h-4 text-gray-400 mb-1" />
                          {location.recentVisitors && location.recentVisitors.length > 0 && (
                            <p className="text-xs text-gray-500">
                              {formatTimeAgo(location.recentVisitors[0].checkedInAt)}
                            </p>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </AnimatePresence>
            
            {nearbyLocations.length === 0 && searchQuery && (
              <div className="text-center py-8 text-gray-500">
                <MapPin className="w-12 h-12 mx-auto mb-2 opacity-50" />
                <p>No places found matching "{searchQuery}"</p>
              </div>
            )}
          </div>

          {/* Check-in message */}
          {selectedLocation && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              className="space-y-3"
            >
              <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                <div className="flex items-center space-x-2 mb-2">
                  <MapPin className="w-4 h-4 text-blue-500" />
                  <span className="font-medium text-blue-900">{selectedLocation.name}</span>
                </div>
                <p className="text-sm text-blue-700">{selectedLocation.address}</p>
              </div>
              
              <Input
                placeholder="Say something about this place... (optional)"
                value={checkInMessage}
                onChange={(e) => setCheckInMessage(e.target.value)}
                maxLength={200}
              />
              
              <div className="flex space-x-2">
                <Button onClick={handleCheckIn} className="flex-1">
                  Check In
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => setSelectedLocation(null)}
                >
                  Cancel
                </Button>
              </div>
            </motion.div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CheckIn;