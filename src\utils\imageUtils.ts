// Image utility functions for safe image handling

// Mock images for development/testing
export const MOCK_IMAGES = {
  placeholder: '/placeholder.svg',
  avatar: '/placeholder.svg',
  video: '/placeholder.svg',
  thumbnail: '/placeholder.svg'
};

/**
 * Returns a safe image URL, falling back to placeholder if the image is invalid
 * @param imageUrl - The original image URL
 * @param fallback - Optional fallback image URL
 * @returns Safe image URL
 */
export const getSafeImage = (imageUrl?: string | null, fallback?: string): string => {
  if (!imageUrl || imageUrl.trim() === '') {
    return fallback || MOCK_IMAGES.placeholder;
  }
  
  // Return the image URL if it's valid
  return imageUrl;
};

/**
 * Validates if an image URL is potentially valid
 * @param url - The URL to validate
 * @returns Boolean indicating if URL appears valid
 */
export const isValidImageUrl = (url?: string | null): boolean => {
  if (!url || typeof url !== 'string') return false;
  
  // Basic URL validation
  try {
    new URL(url);
    return true;
  } catch {
    // Check if it's a relative path
    return url.startsWith('/') || url.startsWith('./') || url.startsWith('../');
  }
};

/**
 * Preloads an image to check if it exists
 * @param src - Image source URL
 * @returns Promise that resolves when image loads or rejects if it fails
 */
export const preloadImage = (src: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve();
    img.onerror = () => reject(new Error(`Failed to load image: ${src}`));
    img.src = src;
  });
};