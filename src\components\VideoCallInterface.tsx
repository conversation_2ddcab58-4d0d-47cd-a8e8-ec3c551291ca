import React, { useState, useRef, useEffect } from 'react';
import {
  <PERSON>,
  VideoOff,
  Mic,
  MicOff,
  PhoneOff,
  Settings,
  Users,
  MessageSquare,
  Maximize,
  Minimize,
  Volume2,
  VolumeX
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';

interface Participant {
  id: string;
  name: string;
  avatar: string;
  isVideoEnabled: boolean;
  isAudioEnabled: boolean;
  isHost: boolean;
  connectionStatus: 'connected' | 'connecting' | 'disconnected';
}

interface VideoCallInterfaceProps {
  participants: Participant[];
  isInCall: boolean;
  callDuration: string;
  onEndCall: () => void;
  onToggleVideo: () => void;
  onToggleMic: () => void;
  onToggleChat: () => void;
  onInviteParticipants: () => void;
  isVideoEnabled: boolean;
  isAudioEnabled: boolean;
  isChatOpen: boolean;
}

const VideoCallInterface: React.FC<VideoCallInterfaceProps> = ({
  participants,
  isInCall,
  callDuration,
  onEndCall,
  onToggleVideo,
  onToggleMic,
  onToggleChat,
  onInviteParticipants,
  isVideoEnabled,
  isAudioEnabled,
  isChatOpen
}) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [showControls, setShowControls] = useState(true);
  const [selectedParticipant, setSelectedParticipant] = useState<string | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const controlsTimeoutRef = useRef<NodeJS.Timeout>();

  // Auto-hide controls after 3 seconds of inactivity
  useEffect(() => {
    if (showControls) {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
      controlsTimeoutRef.current = setTimeout(() => {
        setShowControls(false);
      }, 3000);
    }
    return () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
    };
  }, [showControls]);

  const handleMouseMove = () => {
    setShowControls(true);
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
    if (!isFullscreen) {
      document.documentElement.requestFullscreen?.();
    } else {
      document.exitFullscreen?.();
    }
  };

  const toggleMute = () => {
    setIsMuted(!isMuted);
    toast.info(isMuted ? 'Audio unmuted' : 'Audio muted');
  };

  const handleParticipantClick = (participantId: string) => {
    setSelectedParticipant(participantId);
  };

  if (!isInCall) {
    return null;
  }

  const mainParticipant = selectedParticipant 
    ? participants.find(p => p.id === selectedParticipant)
    : participants.find(p => p.isHost) || participants[0];

  const otherParticipants = participants.filter(p => p.id !== mainParticipant?.id);

  return (
    <div 
      className={`fixed inset-0 bg-black z-50 flex flex-col ${isFullscreen ? 'cursor-none' : ''}`}
      onMouseMove={handleMouseMove}
    >
      {/* Header */}
      <div className={`absolute top-0 left-0 right-0 z-10 bg-gradient-to-b from-black/50 to-transparent p-4 transition-opacity duration-300 ${showControls ? 'opacity-100' : 'opacity-0'}`}>
        <div className="flex items-center justify-between text-white">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
              <span className="text-sm font-medium">{callDuration}</span>
            </div>
            <Badge variant="secondary" className="bg-white/20 text-white">
              {participants.length} participant{participants.length !== 1 ? 's' : ''}
            </Badge>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleFullscreen}
              className="text-white hover:bg-white/20"
            >
              {isFullscreen ? <Minimize className="w-4 h-4" /> : <Maximize className="w-4 h-4" />}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={onInviteParticipants}
              className="text-white hover:bg-white/20"
            >
              <Users className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Main Video Area */}
      <div className="flex-1 relative">
        {/* Main Participant Video */}
        <div className="w-full h-full relative">
          {mainParticipant?.isVideoEnabled ? (
            <video
              ref={videoRef}
              className="w-full h-full object-cover"
              autoPlay
              muted={isMuted}
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gray-900">
              <div className="text-center">
                <Avatar className="w-32 h-32 mx-auto mb-4">
                  <AvatarImage src={mainParticipant?.avatar} />
                  <AvatarFallback className="text-4xl">
                    {mainParticipant?.name.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                <h3 className="text-white text-xl font-semibold">{mainParticipant?.name}</h3>
                <p className="text-gray-400 mt-2">Camera is off</p>
              </div>
            </div>
          )}
          
          {/* Participant Name Overlay */}
          <div className="absolute bottom-4 left-4">
            <div className="bg-black/50 text-white px-3 py-1 rounded-full text-sm">
              {mainParticipant?.name}
              {!mainParticipant?.isAudioEnabled && (
                <MicOff className="w-3 h-3 ml-2 inline" />
              )}
            </div>
          </div>
        </div>

        {/* Other Participants Grid */}
        {otherParticipants.length > 0 && (
          <div className="absolute top-4 right-4 space-y-2 max-w-xs">
            {otherParticipants.map((participant) => (
              <Card 
                key={participant.id}
                className="w-32 h-24 cursor-pointer hover:ring-2 hover:ring-blue-500 transition-all"
                onClick={() => handleParticipantClick(participant.id)}
              >
                <CardContent className="p-0 relative h-full">
                  {participant.isVideoEnabled ? (
                    <video
                      className="w-full h-full object-cover rounded-lg"
                      autoPlay
                      muted
                    />
                  ) : (
                    <div className="w-full h-full bg-gray-800 rounded-lg flex items-center justify-center">
                      <Avatar className="w-8 h-8">
                        <AvatarImage src={participant.avatar} />
                        <AvatarFallback className="text-xs">
                          {participant.name.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                    </div>
                  )}
                  <div className="absolute bottom-1 left-1 text-white text-xs bg-black/50 px-1 rounded">
                    {participant.name.split(' ')[0]}
                  </div>
                  {!participant.isAudioEnabled && (
                    <div className="absolute top-1 right-1">
                      <MicOff className="w-3 h-3 text-red-500" />
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* Controls */}
      <div className={`absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/50 to-transparent p-6 transition-opacity duration-300 ${showControls ? 'opacity-100' : 'opacity-0'}`}>
        <div className="flex items-center justify-center space-x-4">
          <Button
            variant={isAudioEnabled ? "secondary" : "destructive"}
            size="lg"
            onClick={onToggleMic}
            className="rounded-full w-12 h-12"
          >
            {isAudioEnabled ? <Mic className="w-5 h-5" /> : <MicOff className="w-5 h-5" />}
          </Button>
          
          <Button
            variant={isVideoEnabled ? "secondary" : "destructive"}
            size="lg"
            onClick={onToggleVideo}
            className="rounded-full w-12 h-12"
          >
            {isVideoEnabled ? <Video className="w-5 h-5" /> : <VideoOff className="w-5 h-5" />}
          </Button>
          
          <Button
            variant="destructive"
            size="lg"
            onClick={onEndCall}
            className="rounded-full w-12 h-12"
          >
            <PhoneOff className="w-5 h-5" />
          </Button>
          
          <Button
            variant={isMuted ? "destructive" : "secondary"}
            size="lg"
            onClick={toggleMute}
            className="rounded-full w-12 h-12"
          >
            {isMuted ? <VolumeX className="w-5 h-5" /> : <Volume2 className="w-5 h-5" />}
          </Button>
          
          <Button
            variant={isChatOpen ? "default" : "secondary"}
            size="lg"
            onClick={onToggleChat}
            className="rounded-full w-12 h-12"
          >
            <MessageSquare className="w-5 h-5" />
          </Button>
          
          <Button
            variant="secondary"
            size="lg"
            className="rounded-full w-12 h-12"
          >
            <Settings className="w-5 h-5" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default VideoCallInterface;
