import React, { useState } from 'react';
import { Calendar, Clock, Filter, Search, Grid, List, Bookmark, Share, Heart } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { MOCK_IMAGES, getSafeImage } from '@/lib/constants';
import { toast } from 'sonner';

interface MemoryCollection {
  id: string;
  title: string;
  description: string;
  coverImage: string;
  itemCount: number;
  dateRange: string;
  isAutoGenerated: boolean;
  isFavorite: boolean;
  lastUpdated: string;
}

const MemoriesCollection = () => {
  const [collections, setCollections] = useState<MemoryCollection[]>([
    {
      id: '1',
      title: 'Summer Vacation 2023',
      description: 'Our amazing trip to the beach last summer',
      coverImage: MOCK_IMAGES.POSTS[0],
      itemCount: 48,
      dateRange: 'Jun 15 - Jun 30, 2023',
      isAutoGenerated: false,
      isFavorite: true,
      lastUpdated: '2 months ago'
    },
    {
      id: '2',
      title: 'College Graduation',
      description: 'Celebrating the big day with friends and family',
      coverImage: MOCK_IMAGES.POSTS[1],
      itemCount: 32,
      dateRange: 'May 20, 2022',
      isAutoGenerated: false,
      isFavorite: false,
      lastUpdated: '1 year ago'
    },
    {
      id: '3',
      title: '2023 Highlights',
      description: 'Automatically generated collection of your best moments from 2023',
      coverImage: getSafeImage('POSTS', 2),
      itemCount: 124,
      dateRange: 'Jan - Dec 2023',
      isAutoGenerated: true,
      isFavorite: true,
      lastUpdated: '3 weeks ago'
    },
    {
      id: '4',
      title: 'Birthday Celebrations',
      description: 'All your birthday memories through the years',
      coverImage: getSafeImage('POSTS', 3),
      itemCount: 56,
      dateRange: '2018 - 2023',
      isAutoGenerated: true,
      isFavorite: false,
      lastUpdated: '2 days ago'
    }
  ]);

  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('recent');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [filterFavorites, setFilterFavorites] = useState(false);
  const [showFilters, setShowFilters] = useState(false);

  const handleToggleFavorite = (collectionId: string) => {
    setCollections(prev => prev.map(collection => 
      collection.id === collectionId 
        ? { ...collection, isFavorite: !collection.isFavorite }
        : collection
    ));
    
    const collection = collections.find(c => c.id === collectionId);
    if (collection) {
      toast.success(collection.isFavorite 
        ? `Removed "${collection.title}" from favorites` 
        : `Added "${collection.title}" to favorites`);
    }
  };

  const handleShareCollection = (collectionId: string) => {
    const collection = collections.find(c => c.id === collectionId);
    if (collection) {
      toast.success(`Shared "${collection.title}" collection`);
    }
  };

  const handleViewCollection = (collectionId: string) => {
    const collection = collections.find(c => c.id === collectionId);
    if (collection) {
      toast.info(`Viewing "${collection.title}" collection`);
    }
  };

  const handleCreateCollection = () => {
    toast.info('Create new collection feature coming soon!');
  };

  // Filter and sort collections
  const filteredCollections = collections.filter(collection => {
    const matchesSearch = collection.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         collection.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFavorite = filterFavorites ? collection.isFavorite : true;
    return matchesSearch && matchesFavorite;
  });

  const sortedCollections = [...filteredCollections].sort((a, b) => {
    switch (sortBy) {
      case 'recent':
        return new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime();
      case 'oldest':
        return new Date(a.lastUpdated).getTime() - new Date(b.lastUpdated).getTime();
      case 'name':
        return a.title.localeCompare(b.title);
      case 'items':
        return b.itemCount - a.itemCount;
      default:
        return 0;
    }
  });

  return (
    <div className="w-full max-w-6xl mx-auto px-4 py-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-6 gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Memory Collections</h2>
          <p className="text-gray-600 dark:text-gray-300">Organize and revisit your favorite memories</p>
        </div>
        <Button onClick={handleCreateCollection}>
          <Calendar className="w-4 h-4 mr-2" />
          Create Collection
        </Button>
      </div>

      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <Input
            placeholder="Search collections..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          />
        </div>
        <div className="flex gap-2">
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-[140px] dark:bg-gray-700 dark:border-gray-600">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="recent">Most Recent</SelectItem>
              <SelectItem value="oldest">Oldest First</SelectItem>
              <SelectItem value="name">Name (A-Z)</SelectItem>
              <SelectItem value="items">Most Items</SelectItem>
            </SelectContent>
          </Select>
          <Button
            variant="outline"
            size="icon"
            onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
            className="dark:border-gray-600"
          >
            {viewMode === 'grid' ? <List className="h-4 w-4" /> : <Grid className="h-4 w-4" />}
          </Button>
          <Button
            variant={showFilters ? 'default' : 'outline'}
            size="icon"
            onClick={() => setShowFilters(!showFilters)}
            className="dark:border-gray-600"
          >
            <Filter className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="flex flex-wrap gap-4">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="favorites"
                  checked={filterFavorites}
                  onChange={() => setFilterFavorites(!filterFavorites)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 dark:text-blue-400"
                />
                <label htmlFor="favorites" className="text-sm dark:text-gray-200">
                  Favorites only
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="auto"
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 dark:text-blue-400"
                />
                <label htmlFor="auto" className="text-sm dark:text-gray-200">
                  Auto-generated
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="manual"
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 dark:text-blue-400"
                />
                <label htmlFor="manual" className="text-sm dark:text-gray-200">
                  Manually created
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <label htmlFor="year" className="text-sm dark:text-gray-200">
                  Year:
                </label>
                <Select>
                  <SelectTrigger className="w-[100px] dark:bg-gray-700 dark:border-gray-600">
                    <SelectValue placeholder="All" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="2023">2023</SelectItem>
                    <SelectItem value="2022">2022</SelectItem>
                    <SelectItem value="2021">2021</SelectItem>
                    <SelectItem value="2020">2020</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Collections */}
      {sortedCollections.length > 0 ? (
        viewMode === 'grid' ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {sortedCollections.map((collection) => (
              <Card key={collection.id} className="overflow-hidden hover:shadow-md transition-shadow">
                <div className="relative h-48 cursor-pointer" onClick={() => handleViewCollection(collection.id)}>
                  <img
                    src={collection.coverImage}
                    alt={collection.title}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent"></div>
                  <div className="absolute bottom-0 left-0 right-0 p-4 text-white">
                    <h3 className="font-semibold text-lg">{collection.title}</h3>
                    <div className="flex items-center space-x-2 text-sm">
                      <span>{collection.itemCount} items</span>
                      <span>•</span>
                      <span>{collection.dateRange}</span>
                    </div>
                  </div>
                  {collection.isAutoGenerated && (
                    <Badge className="absolute top-2 right-2 bg-blue-500">Auto</Badge>
                  )}
                  {collection.isFavorite && (
                    <div className="absolute top-2 left-2">
                      <Heart className="w-5 h-5 text-red-500 fill-current" />
                    </div>
                  )}
                </div>
                <CardContent className="p-4">
                  <p className="text-sm text-gray-600 mb-4 line-clamp-2 dark:text-gray-300">{collection.description}</p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400">
                      <Clock className="w-3 h-3" />
                      <span>Updated {collection.lastUpdated}</span>
                    </div>
                    <div className="flex space-x-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleToggleFavorite(collection.id)}
                        className={`h-8 w-8 p-0 ${collection.isFavorite ? 'text-red-500' : 'text-gray-500'}`}
                      >
                        <Heart className={`w-4 h-4 ${collection.isFavorite ? 'fill-current' : ''}`} />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleShareCollection(collection.id)}
                        className="h-8 w-8 p-0 text-gray-500"
                      >
                        <Share className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="space-y-4">
            {sortedCollections.map((collection) => (
              <Card key={collection.id} className="overflow-hidden hover:shadow-md transition-shadow">
                <CardContent className="p-0">
                  <div className="flex">
                    <div 
                      className="w-40 h-40 relative flex-shrink-0 cursor-pointer"
                      onClick={() => handleViewCollection(collection.id)}
                    >
                      <img
                        src={collection.coverImage}
                        alt={collection.title}
                        className="w-full h-full object-cover"
                      />
                      {collection.isAutoGenerated && (
                        <Badge className="absolute top-2 right-2 bg-blue-500">Auto</Badge>
                      )}
                      {collection.isFavorite && (
                        <div className="absolute top-2 left-2">
                          <Heart className="w-5 h-5 text-red-500 fill-current" />
                        </div>
                      )}
                    </div>
                    <div className="flex-1 p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h3 
                          className="font-semibold text-lg cursor-pointer hover:text-blue-600 dark:text-white dark:hover:text-blue-400"
                          onClick={() => handleViewCollection(collection.id)}
                        >
                          {collection.title}
                        </h3>
                        <div className="flex space-x-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleToggleFavorite(collection.id)}
                            className={`h-8 w-8 p-0 ${collection.isFavorite ? 'text-red-500' : 'text-gray-500'}`}
                          >
                            <Heart className={`w-4 h-4 ${collection.isFavorite ? 'fill-current' : ''}`} />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleShareCollection(collection.id)}
                            className="h-8 w-8 p-0 text-gray-500"
                          >
                            <Share className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                      <p className="text-sm text-gray-600 mb-3 dark:text-gray-300">{collection.description}</p>
                      <div className="flex flex-wrap gap-2 mb-3">
                        <Badge variant="outline" className="text-xs dark:border-gray-600">
                          {collection.itemCount} items
                        </Badge>
                        <Badge variant="outline" className="text-xs dark:border-gray-600">
                          {collection.dateRange}
                        </Badge>
                      </div>
                      <div className="flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400">
                        <Clock className="w-3 h-3" />
                        <span>Updated {collection.lastUpdated}</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )
      ) : (
        <div className="text-center py-12 bg-white rounded-lg shadow-sm dark:bg-gray-800">
          <Bookmark className="w-16 h-16 text-gray-400 mx-auto mb-4 dark:text-gray-600" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2 dark:text-white">No collections found</h3>
          <p className="text-gray-500 mb-6 dark:text-gray-400">
            {searchQuery 
              ? `No results for "${searchQuery}"`
              : filterFavorites
                ? "You don't have any favorite collections yet"
                : "Start creating collections to organize your memories"}
          </p>
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            {(searchQuery || filterFavorites) && (
              <Button 
                variant="outline" 
                onClick={() => {
                  setSearchQuery('');
                  setFilterFavorites(false);
                }}
                className="dark:border-gray-600 dark:text-gray-200"
              >
                Clear Filters
              </Button>
            )}
            <Button onClick={handleCreateCollection}>
              Create New Collection
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default MemoriesCollection;