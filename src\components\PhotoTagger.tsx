import React, { useState, useRef, useCallback } from 'react';
import { X, Tag } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { toast } from 'sonner';

interface TaggedPerson {
  id: string;
  name: string;
  avatar: string;
  x: number;
  y: number;
}

interface PhotoTaggerProps {
  imageUrl: string;
  onTagsChange: (tags: TaggedPerson[]) => void;
  initialTags?: TaggedPerson[];
  isEditable?: boolean;
}

const PhotoTagger: React.FC<PhotoTaggerProps> = ({
  imageUrl,
  onTagsChange,
  initialTags = [],
  isEditable = true
}) => {
  const [tags, setTags] = useState<TaggedPerson[]>(initialTags);
  const [isTagging, setIsTagging] = useState(false);
  const [pendingTag, setPendingTag] = useState<{ x: number; y: number } | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [showSuggestions, setShowSuggestions] = useState(false);
  const imageRef = useRef<HTMLImageElement>(null);

  // Mock friends data - in real app, this would come from API
  const mockFriends = [
    { id: '1', name: 'John Doe', avatar: 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?w=400&h=400&fit=crop&crop=face' },
    { id: '2', name: 'Jane Smith', avatar: 'https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg?w=400&h=400&fit=crop&crop=face' },
    { id: '3', name: 'Mike Johnson', avatar: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?w=400&h=400&fit=crop&crop=face' },
    { id: '4', name: 'Sarah Wilson', avatar: 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?w=400&h=400&fit=crop&crop=face' }
  ];

  const filteredFriends = mockFriends.filter(friend => 
    friend.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
    !tags.some(tag => tag.id === friend.id)
  );

  const handleImageClick = useCallback((e: React.MouseEvent<HTMLImageElement>) => {
    if (!isEditable || !isTagging) return;

    const rect = e.currentTarget.getBoundingClientRect();
    const x = ((e.clientX - rect.left) / rect.width) * 100;
    const y = ((e.clientY - rect.top) / rect.height) * 100;

    setPendingTag({ x, y });
    setShowSuggestions(true);
    setSearchQuery('');
  }, [isEditable, isTagging]);

  const handleTagPerson = useCallback((person: { id: string; name: string; avatar: string }) => {
    if (!pendingTag) return;

    const newTag: TaggedPerson = {
      ...person,
      x: pendingTag.x,
      y: pendingTag.y
    };

    const updatedTags = [...tags, newTag];
    setTags(updatedTags);
    onTagsChange(updatedTags);
    setPendingTag(null);
    setShowSuggestions(false);
    setIsTagging(false);
    toast.success(`Tagged ${person.name}`);
  }, [pendingTag, tags, onTagsChange]);

  const handleRemoveTag = useCallback((tagId: string) => {
    const updatedTags = tags.filter(tag => tag.id !== tagId);
    setTags(updatedTags);
    onTagsChange(updatedTags);
    toast.success('Tag removed');
  }, [tags, onTagsChange]);

  const cancelTagging = useCallback(() => {
    setPendingTag(null);
    setShowSuggestions(false);
    setIsTagging(false);
  }, []);

  return (
    <div className="relative">
      <div className="relative inline-block">
        <img
          ref={imageRef}
          src={imageUrl}
          alt="Photo to tag"
          className={`max-w-full h-auto rounded-lg ${
            isTagging ? 'cursor-crosshair' : 'cursor-default'
          }`}
          onClick={handleImageClick}
        />

        {/* Existing tags */}
        {tags.map((tag) => (
          <div
            key={tag.id}
            className="absolute transform -translate-x-1/2 -translate-y-1/2 group"
            style={{ left: `${tag.x}%`, top: `${tag.y}%` }}
          >
            <div className="w-8 h-8 bg-blue-500 rounded-full border-2 border-white shadow-lg flex items-center justify-center">
              <Tag className="w-4 h-4 text-white" />
            </div>
            
            {/* Tag tooltip */}
            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 group-hover:opacity-100 transition-opacity">
              <Card className="p-2 shadow-lg">
                <div className="flex items-center space-x-2 whitespace-nowrap">
                  <Avatar className="w-6 h-6">
                    <AvatarImage src={tag.avatar} />
                    <AvatarFallback>{tag.name.charAt(0)}</AvatarFallback>
                  </Avatar>
                  <span className="text-sm font-medium">{tag.name}</span>
                  {isEditable && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-6 h-6 p-0"
                      onClick={() => handleRemoveTag(tag.id)}
                    >
                      <X className="w-3 h-3" />
                    </Button>
                  )}
                </div>
              </Card>
            </div>
          </div>
        ))}

        {/* Pending tag */}
        {pendingTag && (
          <div
            className="absolute transform -translate-x-1/2 -translate-y-1/2"
            style={{ left: `${pendingTag.x}%`, top: `${pendingTag.y}%` }}
          >
            <div className="w-8 h-8 bg-yellow-500 rounded-full border-2 border-white shadow-lg animate-pulse flex items-center justify-center">
              <Tag className="w-4 h-4 text-white" />
            </div>
          </div>
        )}
      </div>

      {/* Tag controls */}
      {isEditable && (
        <div className="mt-4 flex items-center space-x-2">
          <Button
            variant={isTagging ? "default" : "outline"}
            size="sm"
            onClick={() => setIsTagging(!isTagging)}
          >
            <Tag className="w-4 h-4 mr-2" />
            {isTagging ? 'Cancel Tagging' : 'Tag People'}
          </Button>
          
          {tags.length > 0 && (
            <span className="text-sm text-gray-500">
              {tags.length} {tags.length === 1 ? 'person' : 'people'} tagged
            </span>
          )}
        </div>
      )}

      {/* Friend suggestions */}
      {showSuggestions && pendingTag && (
        <Card className="absolute z-50 mt-2 w-80 max-h-60 overflow-y-auto shadow-lg">
          <CardContent className="p-3">
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-medium">Tag someone</h4>
              <Button variant="ghost" size="sm" onClick={cancelTagging}>
                <X className="w-4 h-4" />
              </Button>
            </div>
            
            <Input
              placeholder="Search friends..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="mb-3"
              autoFocus
            />
            
            <div className="space-y-2">
              {filteredFriends.length > 0 ? (
                filteredFriends.map((friend) => (
                  <div
                    key={friend.id}
                    className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100 cursor-pointer transition-colors"
                    onClick={() => handleTagPerson(friend)}
                  >
                    <Avatar className="w-8 h-8">
                      <AvatarImage src={friend.avatar} />
                      <AvatarFallback>{friend.name.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <span className="font-medium">{friend.name}</span>
                  </div>
                ))
              ) : (
                <p className="text-sm text-gray-500 text-center py-4">
                  {searchQuery ? 'No friends found' : 'No friends to tag'}
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default PhotoTagger;