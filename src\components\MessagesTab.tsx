import React from 'react';
import { MessageList, ConversationPane } from './messages';
import { useMessaging } from '@/hooks/useMessaging';

const MessagesTab = () => {
  const {
    conversations,
    messages,
    selectedConversationId,
    searchQuery,
    isMobile,
    showConversation,
    setSearchQuery,
    handleSelectConversation,
    handleSendMessage,
    handleBackToList
  } = useMessaging();

  const selectedConversation = conversations.find(conversation => conversation.id === selectedConversationId);
  const currentMessages = selectedConversationId ? messages[selectedConversationId] : [];

  return (
    <div className="container-responsive mx-auto py-4">
      <div className="max-w-5xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm dark:bg-gray-800 overflow-hidden">
          <div className="flex flex-col md:flex-row h-[calc(100vh-8rem)]">
            {/* Message List Component */}
            <MessageList
              conversations={conversations}
              selectedConversationId={selectedConversationId}
              searchQuery={searchQuery}
              onSearchChange={setSearchQuery}
              onSelectConversation={handleSelectConversation}
              isMobile={isMobile}
              showConversation={showConversation}
            />
            
            {/* Conversation Pane Component */}
            <ConversationPane
              selectedConversation={selectedConversation}
              messages={currentMessages}
              showConversation={showConversation}
              isMobile={isMobile}
              onBackToList={handleBackToList}
              onSendMessage={handleSendMessage}
            />
          </div>
        </div>
      </div>
    </div>
  );
};



export default MessagesTab;