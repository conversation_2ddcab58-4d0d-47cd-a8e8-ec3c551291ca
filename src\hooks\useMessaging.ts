import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import { Conversation, Message } from '@/types/messaging';
import { MOCK_IMAGES } from '@/lib/constants';

interface UseMessagingReturn {
  conversations: Conversation[];
  messages: Record<string, Message[]>;
  selectedConversationId: string | null;
  searchQuery: string;
  isMobile: boolean;
  showConversation: boolean;
  setSearchQuery: (query: string) => void;
  handleSelectConversation: (id: string) => void;
  handleSendMessage: (content: string) => void;
  handleBackToList: () => void;
  setShowConversation: (show: boolean) => void;
}

const INITIAL_CONVERSATIONS: Conversation[] = [
  {
    id: '1',
    type: 'direct',
    user: {
      id: '1',
      name: '<PERSON>',
      avatar: MOCK_IMAGES.AVATARS[0],
      isOnline: true,
      lastActive: 'Active now'
    },
    lastMessage: {
      content: 'Hey! How are you doing?',
      timestamp: '2h',
      isRead: false
    },
    unreadCount: 2
  },
  {
    id: '2',
    type: 'direct',
    user: {
      id: '2',
      name: '<PERSON>',
      avatar: MOC<PERSON>_IMAGES.AVATARS[1],
      isOnline: false,
      lastActive: '3h ago'
    },
    lastMessage: {
      content: 'Did you see that new movie?',
      timestamp: '1d',
      isRead: true
    },
    unreadCount: 0
  },
  {
    id: '3',
    type: 'direct',
    user: {
      id: '3',
      name: 'Emma Wilson',
      avatar: MOCK_IMAGES.AVATARS[2],
      isOnline: true,
      lastActive: 'Active now'
    },
    lastMessage: {
      content: 'Thanks for your help!',
      timestamp: '4d',
      isRead: true
    },
    unreadCount: 0
  }
];

const INITIAL_MESSAGES: Record<string, Message[]> = {
  '1': [
    {
      id: '1-1',
      conversationId: '1',
      content: 'Hey! How are you doing?',
      timestamp: new Date(Date.now() - 3600000 * 3),
      senderId: '1',
      type: 'text',
      status: 'read'
    },
    {
      id: '1-2',
      conversationId: '1',
      content: 'I\'m good, thanks for asking! How about you?',
      timestamp: new Date(Date.now() - 3600000 * 2),
      senderId: 'currentUser',
      type: 'text',
      status: 'read'
    },
    {
      id: '1-3',
      conversationId: '1',
      content: 'I\'m doing great! Just finishing up a project.',
      timestamp: new Date(Date.now() - 3600000 * 1),
      senderId: '1',
      type: 'text',
      status: 'read'
    },
    {
      id: '1-4',
      conversationId: '1',
      content: 'That sounds interesting! What kind of project is it?',
      timestamp: new Date(Date.now() - 1800000),
      senderId: 'currentUser',
      type: 'text',
      status: 'delivered'
    }
  ],
  '2': [
    {
      id: '2-1',
      conversationId: '2',
      content: 'Did you see that new movie?',
      timestamp: new Date(Date.now() - 86400000),
      senderId: '2',
      type: 'text',
      status: 'read'
    }
  ],
  '3': [
    {
      id: '3-1',
      conversationId: '3',
      content: 'Thanks for your help!',
      timestamp: new Date(Date.now() - 86400000 * 4),
      senderId: '3',
      type: 'text',
      status: 'read'
    }
  ]
};

export const useMessaging = (): UseMessagingReturn => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedConversationId, setSelectedConversationId] = useState<string | null>(null);
  const [conversations, setConversations] = useState<Conversation[]>(INITIAL_CONVERSATIONS);
  const [messages, setMessages] = useState<Record<string, Message[]>>(INITIAL_MESSAGES);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  const [showConversation, setShowConversation] = useState(!window.innerWidth < 768);

  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);
      if (!mobile && !selectedConversationId) {
        // On desktop, show the first conversation by default
        setSelectedConversationId(conversations[0].id);
        setShowConversation(true);
      }
    };

    window.addEventListener('resize', handleResize);
    
    // Initialize with first conversation selected on desktop
    if (!isMobile && !selectedConversationId) {
      setSelectedConversationId(conversations[0].id);
      setShowConversation(true);
    }
    
    return () => window.removeEventListener('resize', handleResize);
  }, [isMobile, selectedConversationId, conversations]);

  const handleSelectConversation = useCallback((id: string) => {
    setSelectedConversationId(id);
    
    // Mark conversation as read
    setConversations(prev => prev.map(conversation => 
      conversation.id === id 
        ? { ...conversation, unreadCount: 0 } 
        : conversation
    ));
    
    setShowConversation(true);
  }, []);

  const updateMessageStatus = useCallback((messageId: string, status: Message['status']) => {
    setMessages(prev => {
      const updatedMessages = { ...prev };
      
      // Find the conversation that contains this message
      Object.keys(updatedMessages).forEach(convId => {
        const messageIndex = updatedMessages[convId].findIndex(msg => msg.id === messageId);
        if (messageIndex !== -1) {
          updatedMessages[convId][messageIndex] = {
            ...updatedMessages[convId][messageIndex],
            status
          };
        }
      });
      
      return updatedMessages;
    });
  }, []);

  const simulateReply = useCallback((conversationId: string) => {
    const conversation = conversations.find(c => c.id === conversationId);
    if (!conversation) return;
    
    const replies = [
      "Hey! Good to hear from you!",
      "That sounds interesting!",
      "I'll check it out and get back to you.",
      "Thanks for letting me know!",
      "Let's catch up soon!",
      "I was just thinking about that!",
      "Great, I'll see you then!",
      "Sure thing!",
      "I'm available tomorrow if that works for you.",
      "Can you tell me more about that?"
    ];
    
    const randomReply = replies[Math.floor(Math.random() * replies.length)];
    
    setTimeout(() => {
      // Add the reply to messages
      const replyMessage: Message = {
        id: `${conversationId}-reply-${Date.now()}`,
        conversationId,
        content: randomReply,
        timestamp: new Date(),
        senderId: conversationId,
        type: 'text',
        status: 'read'
      };
      
      setMessages(prev => ({
        ...prev,
        [conversationId]: [...prev[conversationId], replyMessage]
      }));
      
      // Update conversation
      setConversations(prev => prev.map(conv => 
        conv.id === conversationId 
          ? {
              ...conv,
              lastMessage: {
                content: randomReply,
                timestamp: 'Just now',
                isRead: false
              },
              unreadCount: selectedConversationId === conversationId ? 0 : (conv.unreadCount || 0) + 1
            }
          : conv
      ));
      
      // If user is looking at another conversation, show toast
      if (selectedConversationId !== conversationId && conversation.user) {
        toast.info(`New message from ${conversation.user.name}`);
      }
    }, 3000);
  }, [conversations, selectedConversationId]);

  const handleSendMessage = useCallback((content: string) => {
    if (!selectedConversationId) return;
    
    const newMessage: Message = {
      id: `${selectedConversationId}-${Date.now()}`,
      conversationId: selectedConversationId,
      content,
      timestamp: new Date(),
      senderId: 'currentUser',
      type: 'text',
      status: 'sending'
    };
    
    // Add message to state
    setMessages(prev => ({
      ...prev,
      [selectedConversationId]: [...prev[selectedConversationId], newMessage]
    }));
    
    // Update conversation's last message
    setConversations(prev => prev.map(conversation => 
      conversation.id === selectedConversationId 
        ? { 
            ...conversation, 
            lastMessage: {
              content,
              timestamp: 'Just now',
              isRead: true
            }
          } 
        : conversation
    ));
    
    // Simulate message status updates
    setTimeout(() => {
      updateMessageStatus(newMessage.id, 'sent');
      
      setTimeout(() => {
        updateMessageStatus(newMessage.id, 'delivered');
        
        // Simulate read
        setTimeout(() => {
          updateMessageStatus(newMessage.id, 'read');
          
          // Simulate reply after a delay for some conversations
          if (['1', '3'].includes(selectedConversationId) && Math.random() > 0.5) {
            simulateReply(selectedConversationId);
          }
        }, 2000);
      }, 1000);
    }, 500);
    
    toast.success('Message sent');
  }, [selectedConversationId, updateMessageStatus, simulateReply]);

  const handleBackToList = useCallback(() => {
    setShowConversation(false);
  }, []);

  return {
    conversations,
    messages,
    selectedConversationId,
    searchQuery,
    isMobile,
    showConversation,
    setSearchQuery,
    handleSelectConversation,
    handleSendMessage,
    handleBackToList,
    setShowConversation
  };
};