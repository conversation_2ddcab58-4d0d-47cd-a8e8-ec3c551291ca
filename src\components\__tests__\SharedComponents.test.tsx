import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { 
  UserAvatar, 
  InteractionBar, 
  PostHeader, 
  EmptyState, 
  LoadingSkeleton 
} from '../shared/CommonComponents';
import { MessageComposer, SearchInput } from '../shared/FormComponents';
import { BaseModal, ConfirmationModal } from '../shared/ModalComponents';

// Mock user data
const mockUser = {
  id: '1',
  name: '<PERSON>',
  avatar: '/test-avatar.jpg'
};

describe('Shared Components', () => {
  describe('UserAvatar', () => {
    it('renders user avatar with name fallback', () => {
      render(<UserAvatar user={mockUser} />);
      expect(screen.getByText('J')).toBeInTheDocument();
    });

    it('shows online status when enabled', () => {
      render(<UserAvatar user={mockUser} showOnlineStatus isOnline />);
      const onlineIndicator = document.querySelector('.bg-green-500');
      expect(onlineIndicator).toBeInTheDocument();
    });
  });

  describe('InteractionBar', () => {
    const mockProps = {
      likes: 10,
      comments: 5,
      shares: 2,
      isLiked: false,
      isSaved: false,
      onLike: jest.fn(),
      onComment: jest.fn(),
      onShare: jest.fn(),
      onSave: jest.fn()
    };

    it('renders interaction buttons with counts', () => {
      render(<InteractionBar {...mockProps} />);
      expect(screen.getByText('10')).toBeInTheDocument();
      expect(screen.getByText('5')).toBeInTheDocument();
      expect(screen.getByText('2')).toBeInTheDocument();
    });

    it('calls onLike when like button is clicked', () => {
      render(<InteractionBar {...mockProps} />);
      const likeButton = screen.getByRole('button', { name: /10/ });
      fireEvent.click(likeButton);
      expect(mockProps.onLike).toHaveBeenCalled();
    });
  });

  describe('PostHeader', () => {
    const mockProps = {
      user: mockUser,
      timestamp: '2023-01-01T00:00:00Z',
      onUserClick: jest.fn(),
      onMenuClick: jest.fn()
    };

    it('renders user name and timestamp', () => {
      render(<PostHeader {...mockProps} />);
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText(/ago/)).toBeInTheDocument();
    });

    it('shows live badge when isLive is true', () => {
      render(<PostHeader {...mockProps} isLive />);
      expect(screen.getByText('LIVE')).toBeInTheDocument();
    });
  });

  describe('EmptyState', () => {
    const mockProps = {
      icon: <div data-testid="test-icon">Icon</div>,
      title: 'No items found',
      description: 'Try searching for something else',
      action: {
        label: 'Create New',
        onClick: jest.fn()
      }
    };

    it('renders empty state with action button', () => {
      render(<EmptyState {...mockProps} />);
      expect(screen.getByText('No items found')).toBeInTheDocument();
      expect(screen.getByText('Try searching for something else')).toBeInTheDocument();
      expect(screen.getByText('Create New')).toBeInTheDocument();
    });

    it('calls action onClick when button is clicked', () => {
      render(<EmptyState {...mockProps} />);
      const actionButton = screen.getByText('Create New');
      fireEvent.click(actionButton);
      expect(mockProps.action.onClick).toHaveBeenCalled();
    });
  });

  describe('LoadingSkeleton', () => {
    it('renders post skeleton', () => {
      render(<LoadingSkeleton type="post" />);
      const skeleton = document.querySelector('.animate-pulse');
      expect(skeleton).toBeInTheDocument();
    });

    it('renders multiple skeletons when count is specified', () => {
      render(<LoadingSkeleton type="user" count={3} />);
      const skeletons = document.querySelectorAll('.animate-pulse');
      expect(skeletons).toHaveLength(3);
    });
  });

  describe('MessageComposer', () => {
    const mockProps = {
      onSend: jest.fn(),
      placeholder: 'Type a message...'
    };

    it('renders message input with placeholder', () => {
      render(<MessageComposer {...mockProps} />);
      expect(screen.getByPlaceholderText('Type a message...')).toBeInTheDocument();
    });

    it('calls onSend when message is submitted', () => {
      render(<MessageComposer {...mockProps} />);
      const input = screen.getByPlaceholderText('Type a message...');
      const sendButton = screen.getByRole('button');
      
      fireEvent.change(input, { target: { value: 'Test message' } });
      fireEvent.click(sendButton);
      
      expect(mockProps.onSend).toHaveBeenCalledWith('Test message', []);
    });
  });

  describe('SearchInput', () => {
    const mockProps = {
      onSearch: jest.fn(),
      placeholder: 'Search...'
    };

    it('renders search input', () => {
      render(<SearchInput {...mockProps} />);
      expect(screen.getByPlaceholderText('Search...')).toBeInTheDocument();
    });

    it('calls onSearch when input changes', () => {
      jest.useFakeTimers();
      render(<SearchInput {...mockProps} debounceMs={100} />);
      const input = screen.getByPlaceholderText('Search...');
      
      fireEvent.change(input, { target: { value: 'test query' } });
      jest.advanceTimersByTime(100);
      
      expect(mockProps.onSearch).toHaveBeenCalledWith('test query');
      jest.useRealTimers();
    });
  });

  describe('BaseModal', () => {
    const mockProps = {
      isOpen: true,
      onClose: jest.fn(),
      title: 'Test Modal',
      children: <div>Modal content</div>
    };

    it('renders modal when open', () => {
      render(<BaseModal {...mockProps} />);
      expect(screen.getByText('Test Modal')).toBeInTheDocument();
      expect(screen.getByText('Modal content')).toBeInTheDocument();
    });

    it('does not render when closed', () => {
      render(<BaseModal {...mockProps} isOpen={false} />);
      expect(screen.queryByText('Test Modal')).not.toBeInTheDocument();
    });

    it('calls onClose when close button is clicked', () => {
      render(<BaseModal {...mockProps} />);
      const closeButton = screen.getByRole('button');
      fireEvent.click(closeButton);
      expect(mockProps.onClose).toHaveBeenCalled();
    });
  });

  describe('ConfirmationModal', () => {
    const mockProps = {
      isOpen: true,
      onClose: jest.fn(),
      onConfirm: jest.fn(),
      title: 'Confirm Action',
      message: 'Are you sure you want to proceed?'
    };

    it('renders confirmation modal', () => {
      render(<ConfirmationModal {...mockProps} />);
      expect(screen.getByText('Confirm Action')).toBeInTheDocument();
      expect(screen.getByText('Are you sure you want to proceed?')).toBeInTheDocument();
    });

    it('calls onConfirm when confirm button is clicked', () => {
      render(<ConfirmationModal {...mockProps} />);
      const confirmButton = screen.getByText('Confirm');
      fireEvent.click(confirmButton);
      expect(mockProps.onConfirm).toHaveBeenCalled();
    });

    it('calls onClose when cancel button is clicked', () => {
      render(<ConfirmationModal {...mockProps} />);
      const cancelButton = screen.getByText('Cancel');
      fireEvent.click(cancelButton);
      expect(mockProps.onClose).toHaveBeenCalled();
    });
  });
});

// Performance tests
describe('Performance Optimizations', () => {
  it('should render components without performance warnings', () => {
    const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
    
    render(
      <div>
        <UserAvatar user={mockUser} />
        <LoadingSkeleton type="post" />
        <EmptyState 
          icon={<div>Icon</div>}
          title="Test"
          description="Test description"
        />
      </div>
    );
    
    expect(consoleSpy).not.toHaveBeenCalled();
    consoleSpy.mockRestore();
  });
});

// Accessibility tests
describe('Accessibility', () => {
  it('should have proper ARIA labels and roles', () => {
    render(
      <InteractionBar
        likes={10}
        comments={5}
        shares={2}
        isLiked={false}
        isSaved={false}
        onLike={jest.fn()}
        onComment={jest.fn()}
        onShare={jest.fn()}
        onSave={jest.fn()}
      />
    );
    
    const buttons = screen.getAllByRole('button');
    expect(buttons.length).toBeGreaterThan(0);
  });

  it('should support keyboard navigation', () => {
    const onClose = jest.fn();
    render(
      <BaseModal isOpen onClose={onClose} title="Test">
        <div>Content</div>
      </BaseModal>
    );
    
    // Test escape key
    fireEvent.keyDown(document, { key: 'Escape' });
    expect(onClose).toHaveBeenCalled();
  });
});
