import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import React from 'react'
import VideoPlayer from '@/components/VideoPlayer'

// Mock dependencies
vi.mock('@/hooks/useVideoPreferences', () => ({
  useVideoPreferences: () => ({
    recordUserAction: vi.fn(),
    autoplay: false,
    volume: 1,
    playbackRate: 1
  })
}))

vi.mock('sonner', () => ({
  toast: {
    error: vi.fn(),
    info: vi.fn()
  }
}))

vi.mock('@/lib/storage', () => ({
  storage: {
    get: vi.fn().mockReturnValue({}),
    set: vi.fn()
  }
}))

const mockVideo = {
  id: 'test-video-1',
  title: 'Test Video',
  creator: {
    name: 'Test Creator',
    avatar: 'https://example.com/avatar.jpg',
    verified: true
  },
  thumbnail: 'https://example.com/thumbnail.jpg',
  duration: '10:30',
  views: 1000,
  likes: 100,
  timestamp: '2 hours ago',
  description: 'Test video description'
}

// Mock HTMLVideoElement methods
const mockVideoElement = {
  play: vi.fn().mockResolvedValue(undefined),
  pause: vi.fn(),
  load: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  currentTime: 0,
  duration: 630, // 10:30 in seconds
  paused: true,
  muted: false,
  volume: 1,
  readyState: 4,
  networkState: 1,
  error: null
}

describe('VideoPlayer Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Mock querySelector for video element
    vi.spyOn(document, 'querySelector').mockReturnValue(mockVideoElement as Element | null)
    // Mock useRef
    vi.spyOn(React, 'useRef').mockReturnValue({ current: mockVideoElement as HTMLVideoElement | null })
  })

  it('renders video player with correct props', () => {
    render(<VideoPlayer video={mockVideo} />)
    
    expect(screen.getByText('Test Video')).toBeInTheDocument()
    expect(screen.getByText('Test Creator')).toBeInTheDocument()
  })

  it('togglePlay function works correctly with all dependencies', async () => {
    const mockOnTimeUpdate = vi.fn()
    const mockOnEnded = vi.fn()
    
    render(
      <VideoPlayer 
        video={mockVideo} 
        onTimeUpdate={mockOnTimeUpdate}
        onEnded={mockOnEnded}
      />
    )
    
    // Find play button and click it
    const playButton = screen.getByRole('button', { name: /play/i })
    fireEvent.click(playButton)
    
    await waitFor(() => {
      expect(mockVideoElement.play).toHaveBeenCalled()
    })
  })

  it('handles video preferences in togglePlay callback', async () => {
    const mockRecordUserAction = vi.fn()
    
    // Mock useVideoPreferences to return our spy
    vi.doMock('@/hooks/useVideoPreferences', () => ({
      useVideoPreferences: () => ({
        recordUserAction: mockRecordUserAction,
        autoplay: false,
        volume: 1,
        playbackRate: 1
      })
    }))
    
    render(<VideoPlayer video={mockVideo} />)
    
    const playButton = screen.getByRole('button', { name: /play/i })
    fireEvent.click(playButton)
    
    await waitFor(() => {
      expect(mockRecordUserAction).toHaveBeenCalledWith('play')
    })
  })

  it('handles video errors gracefully', () => {
    const videoWithError = { ...mockVideo }
    const mockVideoWithError = {
      ...mockVideoElement,
      error: { code: 4, message: 'Media not supported' }
    }
    
    vi.spyOn(React, 'useRef').mockReturnValue({ current: mockVideoWithError as HTMLVideoElement | null })
    
    render(<VideoPlayer video={videoWithError} />)
    
    const playButton = screen.getByRole('button', { name: /play/i })
    fireEvent.click(playButton)
    
    // Should not attempt to play when there's an error
    expect(mockVideoWithError.play).not.toHaveBeenCalled()
  })

  it('prevents rapid play/pause conflicts', async () => {
    const mockVideoPlaying = {
      ...mockVideoElement,
      paused: false
    }
    
    vi.spyOn(React, 'useRef').mockReturnValue({ current: mockVideoPlaying as HTMLVideoElement | null })
    
    render(<VideoPlayer video={mockVideo} />)
    
    const playButton = screen.getByRole('button', { name: /pause/i })
    
    // Rapid clicks
    fireEvent.click(playButton)
    fireEvent.click(playButton)
    fireEvent.click(playButton)
    
    await waitFor(() => {
      // Should only pause once due to the ref-based conflict prevention
      expect(mockVideoPlaying.pause).toHaveBeenCalledTimes(1)
    })
  })
})
