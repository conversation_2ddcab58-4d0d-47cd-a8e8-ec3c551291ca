import { useCallback, useRef, startTransition } from 'react';
import { toast } from 'sonner';

interface SuspenseError extends Error {
  retryCount?: number;
  timestamp?: number;
  componentName?: string;
}

interface ErrorRetryOptions {
  maxRetries?: number;
  retryDelay?: number;
  exponentialBackoff?: boolean;
  onRetryExhausted?: (error: SuspenseError) => void;
}

interface UseSuspenseErrorHandlerOptions extends ErrorRetryOptions {
  enableLogging?: boolean;
  enableNotifications?: boolean;
}

export const useSuspenseErrorHandler = (
  componentName: string,
  options: UseSuspenseErrorHandlerOptions = {}
) => {
  const {
    maxRetries = 3,
    retryDelay = 1000,
    exponentialBackoff = true,
    enableLogging = true,
    enableNotifications = true,
    onRetryExhausted
  } = options;

  const retryCountRef = useRef<Map<string, number>>(new Map());
  const lastErrorRef = useRef<SuspenseError | null>(null);

  const getRetryCount = useCallback((errorKey: string): number => {
    return retryCountRef.current.get(errorKey) || 0;
  }, []);

  const incrementRetryCount = useCallback((errorKey: string): number => {
    const current = getRetryCount(errorKey);
    const newCount = current + 1;
    retryCountRef.current.set(errorKey, newCount);
    return newCount;
  }, [getRetryCount]);

  const clearRetryCount = useCallback((errorKey: string): void => {
    retryCountRef.current.delete(errorKey);
  }, []);

  const calculateDelay = useCallback((retryCount: number): number => {
    if (!exponentialBackoff) {
      return retryDelay;
    }
    return Math.min(retryDelay * Math.pow(2, retryCount - 1), 10000); // Max 10s
  }, [retryDelay, exponentialBackoff]);

  const createErrorKey = useCallback((error: Error): string => {
    return `${componentName}_${error.name}_${error.message}`.substring(0, 100);
  }, [componentName]);

  const handleSuspenseError = useCallback((
    error: Error,
    retryFn: () => void,
    fallbackFn?: () => void
  ): void => {
    const suspenseError: SuspenseError = {
      ...error,
      componentName,
      timestamp: Date.now()
    };

    lastErrorRef.current = suspenseError;
    const errorKey = createErrorKey(error);
    const currentRetryCount = incrementRetryCount(errorKey);

    if (enableLogging) {
      console.error(`Suspense error in ${componentName} (attempt ${currentRetryCount}):`, error);
    }

    if (currentRetryCount <= maxRetries) {
      const delay = calculateDelay(currentRetryCount);
      
      if (enableNotifications) {
        toast.loading(`Retrying... (${currentRetryCount}/${maxRetries})`, {
          id: errorKey,
          duration: delay
        });
      }

      setTimeout(() => {
        startTransition(() => {
          retryFn();
        });
        
        if (enableNotifications) {
          toast.dismiss(errorKey);
        }
      }, delay);

    } else {
      // Max retries reached
      clearRetryCount(errorKey);
      
      if (enableNotifications) {
        toast.dismiss(errorKey);
        toast.error('Failed to load component. Please refresh the page.', {
          duration: 5000,
          action: {
            label: 'Refresh',
            onClick: () => window.location.reload()
          }
        });
      }

      if (onRetryExhausted) {
        onRetryExhausted(suspenseError);
      } else if (fallbackFn) {
        fallbackFn();
      }
    }
  }, [
    componentName,
    maxRetries,
    enableLogging,
    enableNotifications,
    calculateDelay,
    createErrorKey,
    incrementRetryCount,
    clearRetryCount,
    onRetryExhausted
  ]);

  const wrapWithErrorHandling = useCallback(<T extends (...args: unknown[]) => unknown>(
    asyncFn: T,
    retryFn?: () => void,
    fallbackFn?: () => void
  ): T => {
    return ((...args: Parameters<T>) => {
      try {
        const result = asyncFn(...args);
        
        // Handle promises
        if (result && typeof result.catch === 'function') {
          return result.catch((error: Error) => {
            handleSuspenseError(error, retryFn || (() => asyncFn(...args)), fallbackFn);
            throw error; // Re-throw to trigger Suspense
          });
        }
        
        return result;
      } catch (error) {
        if (error instanceof Error) {
          handleSuspenseError(error, retryFn || (() => asyncFn(...args)), fallbackFn);
        }
        throw error; // Re-throw to trigger Suspense
      }
    }) as T;
  }, [handleSuspenseError]);

  const createSuspenseResource = useCallback(<T>(
    fetcher: () => Promise<T>,
    retryFn?: () => void
  ) => {
    let status = 'pending';
    let result: T;
    const suspender: Promise<void> = wrappedFetcher()

    const wrappedFetcher = wrapWithErrorHandling(fetcher, retryFn);

    suspender = wrappedFetcher()
      .then((data: T) => {
        status = 'success';
        result = data;
      })
      .catch((error: Error) => {
        status = 'error';
        result = error as unknown as T;
      });

    return {
      read(): T {
        if (status === 'pending') {
          throw suspender;
        } else if (status === 'error') {
          throw result;
        }
        return result;
      },
      preload: () => suspender
    };
  }, [wrapWithErrorHandling]);

  const resetErrorState = useCallback(() => {
    retryCountRef.current.clear();
    lastErrorRef.current = null;
  }, []);

  const getErrorStats = useCallback(() => {
    const totalErrors = Array.from(retryCountRef.current.values())
      .reduce((sum, count) => sum + count, 0);
    
    return {
      totalErrors,
      uniqueErrors: retryCountRef.current.size,
      lastError: lastErrorRef.current,
      activeRetries: Array.from(retryCountRef.current.entries())
        .filter(([, count]) => count > 0)
    };
  }, []);

  return {
    handleSuspenseError,
    wrapWithErrorHandling,
    createSuspenseResource,
    resetErrorState,
    getErrorStats,
    lastError: lastErrorRef.current
  };
};

export default useSuspenseErrorHandler;
