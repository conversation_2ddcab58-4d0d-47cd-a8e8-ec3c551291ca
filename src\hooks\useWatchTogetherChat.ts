import { useState, useRef, useEffect } from 'react';

interface ChatMessage {
  id: string;
  senderId: string;
  senderName: string;
  senderAvatar: string;
  message: string;
  timestamp: Date;
}

interface Participant {
  id: string;
  name: string;
  avatar: string;
  isHost: boolean;
  isMuted: boolean;
  isVideoOn: boolean;
  isSpeaking?: boolean;
}

interface UseWatchTogetherChatReturn {
  chatMessages: ChatMessage[];
  newMessage: string;
  setNewMessage: (message: string) => void;
  handleSendMessage: () => void;
  chatContainerRef: React.RefObject<HTMLDivElement>;
}

const MOCK_CHAT_MESSAGES = [
  "This part is so good!",
  "Wait, did you see that?",
  "I can't believe this scene, wow!",
  "I've watched this 5 times already",
  "The soundtrack is amazing",
  "Who's your favorite character?",
  "This reminds me of that other movie we watched",
  "Let's watch the sequel next time"
] as const;

export const useWatchTogetherChat = (
  participants: Participant[],
  setParticipants: React.Dispatch<React.SetStateAction<Participant[]>>,
  currentUserAvatar: string
): UseWatchTogetherChatReturn => {
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const chatContainerRef = useRef<HTMLDivElement>(null);

  // Simulate receiving chat messages from other participants
  useEffect(() => {
    const chatInterval = setInterval(() => {
      if (Math.random() > 0.7) {
        const participant = participants.find(p => p.id !== 'user-1');
        if (participant) {
          const randomMessage = MOCK_CHAT_MESSAGES[Math.floor(Math.random() * MOCK_CHAT_MESSAGES.length)];
          
          const newChatMessage: ChatMessage = {
            id: `msg-${Date.now()}`,
            senderId: participant.id,
            senderName: participant.name,
            senderAvatar: participant.avatar,
            message: randomMessage,
            timestamp: new Date()
          };
          
          setChatMessages(prev => [...prev, newChatMessage]);
          
          // Update speaking status
          setParticipants(prev => prev.map(p => 
            p.id === participant.id 
              ? { ...p, isSpeaking: true }
              : p.isSpeaking ? { ...p, isSpeaking: false } : p
          ));
          
          // Reset speaking after a delay
          setTimeout(() => {
            setParticipants(prev => prev.map(p => 
              p.id === participant.id ? { ...p, isSpeaking: false } : p
            ));
          }, 2000);
        }
      }
    }, 8000);
    
    return () => clearInterval(chatInterval);
  }, [participants, setParticipants]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [chatMessages]);

  const handleSendMessage = () => {
    if (!newMessage.trim()) return;
    
    const message: ChatMessage = {
      id: `msg-${Date.now()}`,
      senderId: 'user-1',
      senderName: 'You',
      senderAvatar: currentUserAvatar,
      message: newMessage,
      timestamp: new Date()
    };
    
    setChatMessages(prev => [...prev, message]);
    setNewMessage('');
  };

  return {
    chatMessages,
    newMessage,
    setNewMessage,
    handleSendMessage,
    chatContainerRef
  };
};