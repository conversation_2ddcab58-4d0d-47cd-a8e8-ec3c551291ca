import React, { useState, useRef, useCallback, useEffect } from 'react';
import { X, Tag, User, Check, Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

export interface PhotoTag {
  id: string;
  friendId: string;
  friendName: string;
  friendAvatar: string;
  x: number; // Percentage from left (0-100)
  y: number; // Percentage from top (0-100)
  confirmed: boolean;
  createdBy: string;
  createdAt: Date;
}

export interface Friend {
  id: string;
  name: string;
  avatar: string;
  isOnline?: boolean;
  mutualFriends?: number;
}

interface AdvancedPhotoTaggerProps {
  imageUrl: string;
  existingTags?: PhotoTag[];
  onTagsChange: (tags: PhotoTag[]) => void;
  onClose: () => void;
  isOpen: boolean;
  canTag?: boolean;
  currentUserId?: string;
  className?: string;
}

const AdvancedPhotoTagger: React.FC<AdvancedPhotoTaggerProps> = ({
  imageUrl,
  existingTags = [],
  onTagsChange,
  onClose,
  isOpen,
  canTag = true,
  currentUserId = 'current_user',
  className
}) => {
  const [tags, setTags] = useState<PhotoTag[]>(existingTags);
  const [isTagging, setIsTagging] = useState(false);
  const [pendingTag, setPendingTag] = useState<{ x: number; y: number } | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showTagSuggestions, setShowTagSuggestions] = useState(false);
  const [hoveredTag, setHoveredTag] = useState<string | null>(null);
  const [showAllTags, setShowAllTags] = useState(false);

  const imageRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Mock friends data - in real app this would come from API
  const friends: Friend[] = [
    { id: '1', name: 'Sarah Johnson', avatar: '/placeholder.svg', isOnline: true, mutualFriends: 23 },
    { id: '2', name: 'Mike Chen', avatar: '/placeholder.svg', isOnline: false, mutualFriends: 45 },
    { id: '3', name: 'Emma Wilson', avatar: '/placeholder.svg', isOnline: true, mutualFriends: 12 },
    { id: '4', name: 'David Kim', avatar: '/placeholder.svg', isOnline: false, mutualFriends: 34 },
    { id: '5', name: 'Lisa Wang', avatar: '/placeholder.svg', isOnline: true, mutualFriends: 56 },
    { id: '6', name: 'Alex Rodriguez', avatar: '/placeholder.svg', isOnline: false, mutualFriends: 18 },
    { id: '7', name: 'Jessica Park', avatar: '/placeholder.svg', isOnline: true, mutualFriends: 27 }
  ];

  const filteredFriends = friends.filter(friend =>
    friend.name.toLowerCase().includes(searchTerm.toLowerCase()) &&
    !tags.some(tag => tag.friendId === friend.id)
  );

  const handleImageClick = useCallback((event: React.MouseEvent<HTMLImageElement>) => {
    if (!canTag || !isTagging) return;

    const rect = event.currentTarget.getBoundingClientRect();
    const x = ((event.clientX - rect.left) / rect.width) * 100;
    const y = ((event.clientY - rect.top) / rect.height) * 100;

    setPendingTag({ x, y });
    setShowTagSuggestions(true);
    setSearchTerm('');
  }, [canTag, isTagging]);

  const handleTagFriend = useCallback((friend: Friend) => {
    if (!pendingTag) return;

    const newTag: PhotoTag = {
      id: `tag_${Date.now()}`,
      friendId: friend.id,
      friendName: friend.name,
      friendAvatar: friend.avatar,
      x: pendingTag.x,
      y: pendingTag.y,
      confirmed: false,
      createdBy: currentUserId,
      createdAt: new Date()
    };

    const updatedTags = [...tags, newTag];
    setTags(updatedTags);
    onTagsChange(updatedTags);
    
    setPendingTag(null);
    setShowTagSuggestions(false);
    setIsTagging(false);
    
    toast.success(`Tagged ${friend.name} in photo`);
  }, [pendingTag, tags, onTagsChange, currentUserId]);

  const handleRemoveTag = useCallback((tagId: string) => {
    const updatedTags = tags.filter(tag => tag.id !== tagId);
    setTags(updatedTags);
    onTagsChange(updatedTags);
    toast.info('Tag removed');
  }, [tags, onTagsChange]);

  const handleConfirmTag = useCallback((tagId: string) => {
    const updatedTags = tags.map(tag =>
      tag.id === tagId ? { ...tag, confirmed: true } : tag
    );
    setTags(updatedTags);
    onTagsChange(updatedTags);
    toast.success('Tag confirmed');
  }, [tags, onTagsChange]);

  const cancelTagging = useCallback(() => {
    setIsTagging(false);
    setPendingTag(null);
    setShowTagSuggestions(false);
  }, []);

  // Auto-cancel tagging when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node) &&
        showTagSuggestions
      ) {
        cancelTagging();
      }
    };

    if (showTagSuggestions) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showTagSuggestions, cancelTagging]);

  // Sync with prop changes
  useEffect(() => {
    setTags(existingTags);
  }, [existingTags]);

  const getTagPosition = (tag: PhotoTag) => ({
    left: `${Math.max(0, Math.min(95, tag.x))}%`,
    top: `${Math.max(0, Math.min(95, tag.y))}%`
  });

  const getPendingTagPosition = () => {
    if (!pendingTag) return {};
    return {
      left: `${Math.max(0, Math.min(95, pendingTag.x))}%`,
      top: `${Math.max(0, Math.min(95, pendingTag.y))}%`
    };
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] p-0">
        <DialogHeader className="p-6 pb-0">
          <DialogTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Tag className="w-5 h-5" />
              <span>Tag People in Photo</span>
            </div>
            <div className="flex items-center space-x-2">
              {canTag && (
                <Button
                  variant={isTagging ? "default" : "outline"}
                  size="sm"
                  onClick={() => setIsTagging(!isTagging)}
                  className="flex items-center space-x-2"
                >
                  <Tag className="w-4 h-4" />
                  <span>{isTagging ? 'Stop Tagging' : 'Add Tag'}</span>
                </Button>
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowAllTags(!showAllTags)}
                className="flex items-center space-x-2"
              >
                <User className="w-4 h-4" />
                <span>{showAllTags ? 'Hide Tags' : 'Show All Tags'}</span>
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="relative flex-1 p-6 pt-0">
          <div
            ref={containerRef}
            className={cn(
              'relative inline-block max-w-full',
              isTagging && 'cursor-crosshair',
              className
            )}
          >
            <img
              ref={imageRef}
              src={imageUrl}
              alt="Photo to tag"
              className="max-w-full max-h-[60vh] object-contain rounded-lg"
              onClick={handleImageClick}
              onMouseEnter={() => setShowAllTags(true)}
              onMouseLeave={() => setShowAllTags(false)}
            />

            {/* Existing Tags */}
            <AnimatePresence>
              {tags.map((tag) => (
                <motion.div
                  key={tag.id}
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  exit={{ scale: 0 }}
                  className="absolute"
                  style={getTagPosition(tag)}
                  onMouseEnter={() => setHoveredTag(tag.id)}
                  onMouseLeave={() => setHoveredTag(null)}
                >
                  {/* Tag Marker */}
                  <div className="relative">
                    <div className={cn(
                      'w-8 h-8 border-2 border-white rounded-full shadow-lg cursor-pointer transform -translate-x-1/2 -translate-y-1/2',
                      tag.confirmed 
                        ? 'bg-blue-600' 
                        : 'bg-yellow-500 animate-pulse'
                    )}>
                      <Avatar className="w-full h-full">
                        <AvatarImage src={tag.friendAvatar} />
                        <AvatarFallback className="text-xs">
                          {tag.friendName[0]}
                        </AvatarFallback>
                      </Avatar>
                    </div>

                    {/* Tag Label */}
                    <AnimatePresence>
                      {(hoveredTag === tag.id || showAllTags) && (
                        <motion.div
                          initial={{ opacity: 0, y: 5 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: 5 }}
                          className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 translate-y-full"
                        >
                          <div className="bg-black/80 text-white text-xs px-2 py-1 rounded-md whitespace-nowrap flex items-center space-x-1">
                            <span>{tag.friendName}</span>
                            {!tag.confirmed && (
                              <Badge variant="secondary" className="text-[10px] px-1">
                                Pending
                              </Badge>
                            )}
                          </div>
                          
                          {/* Action Buttons */}
                          <div className="flex items-center justify-center space-x-1 mt-1">
                            {!tag.confirmed && tag.createdBy === currentUserId && (
                              <Button
                                size="sm"
                                variant="default"
                                className="h-6 w-6 p-0 bg-green-600 hover:bg-green-700"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleConfirmTag(tag.id);
                                }}
                                title="Confirm tag"
                              >
                                <Check className="w-3 h-3" />
                              </Button>
                            )}
                            
                            {canTag && (
                              <Button
                                size="sm"
                                variant="destructive"
                                className="h-6 w-6 p-0"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleRemoveTag(tag.id);
                                }}
                                title="Remove tag"
                              >
                                <X className="w-3 h-3" />
                              </Button>
                            )}
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>

            {/* Pending Tag */}
            {pendingTag && (
              <div
                className="absolute w-8 h-8 border-2 border-dashed border-blue-500 rounded-full bg-blue-500/20 transform -translate-x-1/2 -translate-y-1/2 animate-pulse"
                style={getPendingTagPosition()}
              />
            )}

            {/* Friend Suggestions Popup */}
            <AnimatePresence>
              {showTagSuggestions && pendingTag && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.9 }}
                  className="absolute z-10"
                  style={{
                    left: `${Math.max(10, Math.min(70, pendingTag.x))}%`,
                    top: `${Math.max(10, Math.min(70, pendingTag.y + 5))}%`
                  }}
                >
                  <Card className="w-80 shadow-lg">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h3 className="font-medium text-sm">Tag a friend</h3>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={cancelTagging}
                          className="h-6 w-6 p-0"
                        >
                          <X className="w-4 h-4" />
                        </Button>
                      </div>

                      <div className="relative mb-3">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                        <Input
                          placeholder="Search friends..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          className="pl-10"
                          autoFocus
                        />
                      </div>

                      <div className="max-h-48 overflow-y-auto space-y-1">
                        {filteredFriends.length > 0 ? (
                          filteredFriends.slice(0, 8).map((friend) => (
                            <button
                              key={friend.id}
                              onClick={() => handleTagFriend(friend)}
                              className="w-full flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                            >
                              <Avatar className="w-8 h-8">
                                <AvatarImage src={friend.avatar} />
                                <AvatarFallback>{friend.name[0]}</AvatarFallback>
                              </Avatar>
                              <div className="flex-1 text-left">
                                <div className="font-medium text-sm">{friend.name}</div>
                                <div className="text-xs text-gray-500 flex items-center space-x-2">
                                  {friend.isOnline && (
                                    <Badge variant="secondary" className="text-[10px] px-1">
                                      Online
                                    </Badge>
                                  )}
                                  {friend.mutualFriends && (
                                    <span>{friend.mutualFriends} mutual friends</span>
                                  )}
                                </div>
                              </div>
                            </button>
                          ))
                        ) : (
                          <div className="text-center py-4 text-gray-500 text-sm">
                            {searchTerm ? 'No friends found' : 'Start typing to search friends'}
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Instructions */}
          {isTagging && !pendingTag && (
            <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <p className="text-sm text-blue-700 dark:text-blue-300">
                Click on the photo where you want to tag someone
              </p>
            </div>
          )}

          {/* Tagged People Summary */}
          {tags.length > 0 && (
            <div className="mt-4">
              <h4 className="font-medium text-sm mb-2">Tagged People ({tags.length})</h4>
              <div className="flex flex-wrap gap-2">
                {tags.map((tag) => (
                  <Badge
                    key={tag.id}
                    variant={tag.confirmed ? "default" : "secondary"}
                    className="flex items-center space-x-1"
                  >
                    <Avatar className="w-4 h-4">
                      <AvatarImage src={tag.friendAvatar} />
                      <AvatarFallback className="text-[10px]">
                        {tag.friendName[0]}
                      </AvatarFallback>
                    </Avatar>
                    <span>{tag.friendName}</span>
                    {!tag.confirmed && <span className="text-[10px]">(pending)</span>}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AdvancedPhotoTagger;
