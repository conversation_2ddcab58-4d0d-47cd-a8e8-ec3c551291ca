import { useState, useEffect, useRef } from 'react';

interface UseControlsVisibilityReturn {
  showControls: boolean;
  setShowControls: (show: boolean) => void;
  handleMouseMove: () => void;
}

const CONTROLS_HIDE_DELAY = 3000;

export const useControlsVisibility = (
  isOpen: boolean,
  isPlaying: boolean
): UseControlsVisibilityReturn => {
  const [showControls, setShowControls] = useState(true);
  const controlsTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Auto-hide controls when video is playing
  useEffect(() => {
    const hideControls = () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
      
      controlsTimeoutRef.current = setTimeout(() => {
        if (isPlaying) {
          setShowControls(false);
        }
      }, CONTROLS_HIDE_DELAY);
    };
    
    if (isOpen) {
      hideControls();
    }
    
    return () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
    };
  }, [isOpen, isPlaying, showControls]);

  const handleMouseMove = () => {
    setShowControls(true);
    
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current);
    }
    
    controlsTimeoutRef.current = setTimeout(() => {
      if (isPlaying) {
        setShowControls(false);
      }
    }, CONTROLS_HIDE_DELAY);
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
    };
  }, []);

  return {
    showControls,
    setShowControls,
    handleMouseMove
  };
};