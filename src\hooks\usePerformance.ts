import React, { useCallback, useEffect, useRef, startTransition, useDeferredValue } from 'react';

interface PerformanceMetrics {
  renderTime: number;
  componentCount: number;
  memoryUsage?: number;
}

interface UsePerformanceOptions {
  enableProfiling?: boolean;
  enableMemoryTracking?: boolean;
  onMetrics?: (metrics: PerformanceMetrics) => void;
}

export const usePerformance = (
  componentName: string,
  options: UsePerformanceOptions = {}
) => {
  const { enableProfiling = false, enableMemoryTracking = false, onMetrics } = options;
  
  const renderStartTime = useRef<number>();
  const componentCountRef = useRef<number>(0);
  const metricsRef = useRef<PerformanceMetrics[]>([]);

  // Track component renders
  useEffect(() => {
    componentCountRef.current++;
    
    if (enableProfiling) {
      const renderEndTime = performance.now();
      const renderTime = renderStartTime.current 
        ? renderEndTime - renderStartTime.current 
        : 0;

      const metrics: PerformanceMetrics = {
        renderTime,
        componentCount: componentCountRef.current,
        ...(enableMemoryTracking && getMemoryUsage())
      };

      metricsRef.current.push(metrics);
      onMetrics?.(metrics);

      // Log performance in development
      if (process.env.NODE_ENV === 'development') {
        console.log(`Performance [${componentName}]:`, metrics);
      }
    }
  });

  // Mark render start
  if (enableProfiling) {
    renderStartTime.current = performance.now();
  }

  // Get memory usage (if available)
  const getMemoryUsage = () => {
    interface PerformanceMemory {
      usedJSHeapSize: number;
      totalJSHeapSize: number;
      jsHeapSizeLimit: number;
    }
    
    interface ExtendedPerformance extends Performance {
      memory?: PerformanceMemory;
    }
    
    if (typeof (performance as ExtendedPerformance).memory !== 'undefined') {
      const memory = (performance as ExtendedPerformance).memory!;
      return {
        memoryUsage: memory.usedJSHeapSize / 1024 / 1024 // MB
      };
    }
    return {};
  };

  // Optimized state update with startTransition
  const updateWithTransition = useCallback((updateFn: () => void) => {
    startTransition(() => {
      updateFn();
    });
  }, []);

  // Deferred value for non-urgent updates
  const useDeferredState = <T>(value: T): T => {
    return useDeferredValue(value);
  };

  // Measure and optimize expensive computations
  const measureComputation = useCallback(<T>(
    computation: () => T,
    label: string
  ): T => {
    if (enableProfiling) {
      const start = performance.now();
      const result = computation();
      const end = performance.now();
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`Computation [${componentName}:${label}]: ${end - start}ms`);
      }
      
      return result;
    }
    
    return computation();
  }, [componentName, enableProfiling]);

  // Get performance summary
  const getPerformanceSummary = useCallback(() => {
    const metrics = metricsRef.current;
    if (metrics.length === 0) return null;

    const avgRenderTime = metrics.reduce((sum, m) => sum + m.renderTime, 0) / metrics.length;
    const maxRenderTime = Math.max(...metrics.map(m => m.renderTime));
    const totalRenders = metrics.length;

    return {
      componentName,
      avgRenderTime: Math.round(avgRenderTime * 100) / 100,
      maxRenderTime: Math.round(maxRenderTime * 100) / 100,
      totalRenders,
      lastMemoryUsage: enableMemoryTracking ? metrics[metrics.length - 1]?.memoryUsage : undefined
    };
  }, [componentName, enableMemoryTracking]);

  // Clear metrics
  const clearMetrics = useCallback(() => {
    metricsRef.current = [];
    componentCountRef.current = 0;
  }, []);

  return {
    updateWithTransition,
    useDeferredState,
    measureComputation,
    getPerformanceSummary,
    clearMetrics,
    renderCount: componentCountRef.current
  };
};

// HOC for automatic performance tracking
export const withPerformanceTracking = <P extends object>(
  Component: React.ComponentType<P>,
  componentName?: string
) => {
  const WrappedComponent = (props: P) => {
    const perfName = componentName || Component.displayName || Component.name || 'Unknown';
    
    usePerformance(perfName, {
      enableProfiling: process.env.NODE_ENV === 'development',
      enableMemoryTracking: true
    });

    return React.createElement(Component, props);
  };

  WrappedComponent.displayName = `withPerformanceTracking(${componentName || Component.displayName || Component.name})`;
  
  return WrappedComponent;
};

export default usePerformance;
