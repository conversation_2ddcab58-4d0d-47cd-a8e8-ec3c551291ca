import React, { useState, useCallback } from 'react';
import { Shield, Eye, Users, Globe, Lock, UserCheck, Bell, MapPin, Camera, MessageCircle, Heart } from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { motion } from 'framer-motion';

interface PrivacyOption {
  id: string;
  title: string;
  description: string;
  value: 'public' | 'friends' | 'friends_except' | 'specific_friends' | 'only_me' | 'custom';
  icon: React.ReactNode;
}

interface NotificationSetting {
  id: string;
  title: string;
  description: string;
  enabled: boolean;
  category: 'posts' | 'messages' | 'friends' | 'pages' | 'events' | 'security';
}

interface PrivacySettings {
  posts: {
    whoCanSeeMyPosts: string;
    whoCanSeeMyFriendsList: string;
    whoCanLookMeUp: string;
    whoCanSendMeFriendRequests: string;
  };
  timeline: {
    whoCanPostOnMyTimeline: string;
    whoCanSeePostsOnMyTimeline: string;
    reviewPostsBeforeShowing: boolean;
    reviewTagsBeforeShowing: boolean;
  };
  profile: {
    whoCanSeeMyEmail: string;
    whoCanSeeMyPhoneNumber: string;
    whoCanSeeMyBirthday: string;
    whoCanSeeMyRelationshipStatus: string;
  };
  location: {
    locationServices: boolean;
    shareLocationInPosts: boolean;
    nearbyFriends: boolean;
  };
  blocking: {
    blockedUsers: string[];
    blockedPages: string[];
    blockedApps: string[];
  };
  notifications: NotificationSetting[];
}

const PrivacySettingsComponent: React.FC = () => {
  const [settings, setSettings] = useState<PrivacySettings>({
    posts: {
      whoCanSeeMyPosts: 'friends',
      whoCanSeeMyFriendsList: 'friends',
      whoCanLookMeUp: 'everyone',
      whoCanSendMeFriendRequests: 'everyone'
    },
    timeline: {
      whoCanPostOnMyTimeline: 'friends',
      whoCanSeePostsOnMyTimeline: 'friends',
      reviewPostsBeforeShowing: true,
      reviewTagsBeforeShowing: true
    },
    profile: {
      whoCanSeeMyEmail: 'only_me',
      whoCanSeeMyPhoneNumber: 'only_me',
      whoCanSeeMyBirthday: 'friends',
      whoCanSeeMyRelationshipStatus: 'friends'
    },
    location: {
      locationServices: true,
      shareLocationInPosts: false,
      nearbyFriends: false
    },
    blocking: {
      blockedUsers: [],
      blockedPages: [],
      blockedApps: []
    },
    notifications: [
      {
        id: 'post_likes',
        title: 'Likes on your posts',
        description: 'Get notified when someone likes your posts',
        enabled: true,
        category: 'posts'
      },
      {
        id: 'post_comments',
        title: 'Comments on your posts',
        description: 'Get notified when someone comments on your posts',
        enabled: true,
        category: 'posts'
      },
      {
        id: 'post_shares',
        title: 'Shares of your posts',
        description: 'Get notified when someone shares your posts',
        enabled: true,
        category: 'posts'
      },
      {
        id: 'friend_requests',
        title: 'Friend requests',
        description: 'Get notified when you receive friend requests',
        enabled: true,
        category: 'friends'
      },
      {
        id: 'friend_birthdays',
        title: 'Friend birthdays',
        description: 'Get notified about your friends\' birthdays',
        enabled: true,
        category: 'friends'
      },
      {
        id: 'messages',
        title: 'New messages',
        description: 'Get notified when you receive new messages',
        enabled: true,
        category: 'messages'
      },
      {
        id: 'login_alerts',
        title: 'Login alerts',
        description: 'Get notified when someone logs into your account',
        enabled: true,
        category: 'security'
      },
      {
        id: 'password_changes',
        title: 'Password changes',
        description: 'Get notified when your password is changed',
        enabled: true,
        category: 'security'
      }
    ]
  });

  const privacyOptions: PrivacyOption[] = [
    {
      id: 'public',
      title: 'Public',
      description: 'Anyone on or off Facebook',
      value: 'public',
      icon: <Globe className="w-4 h-4" />
    },
    {
      id: 'friends',
      title: 'Friends',
      description: 'Your friends on Facebook',
      value: 'friends',
      icon: <Users className="w-4 h-4" />
    },
    {
      id: 'friends_except',
      title: 'Friends except...',
      description: 'Friends except specific people',
      value: 'friends_except',
      icon: <UserCheck className="w-4 h-4" />
    },
    {
      id: 'specific_friends',
      title: 'Specific friends',
      description: 'Only specific friends',
      value: 'specific_friends',
      icon: <UserCheck className="w-4 h-4" />
    },
    {
      id: 'only_me',
      title: 'Only me',
      description: 'Only you can see this',
      value: 'only_me',
      icon: <Lock className="w-4 h-4" />
    }
  ];

  const updatePostsSetting = useCallback((key: keyof typeof settings.posts, value: string) => {
    setSettings(prev => ({
      ...prev,
      posts: {
        ...prev.posts,
        [key]: value
      }
    }));
    toast.success('Privacy setting updated');
  }, [settings]);

  const updateTimelineSetting = useCallback((key: keyof typeof settings.timeline, value: string | boolean) => {
    setSettings(prev => ({
      ...prev,
      timeline: {
        ...prev.timeline,
        [key]: value
      }
    }));
    toast.success('Timeline setting updated');
  }, [settings]);

  const updateProfileSetting = useCallback((key: keyof typeof settings.profile, value: string) => {
    setSettings(prev => ({
      ...prev,
      profile: {
        ...prev.profile,
        [key]: value
      }
    }));
    toast.success('Profile setting updated');
  }, [settings]);

  const updateLocationSetting = useCallback((key: keyof typeof settings.location, value: boolean) => {
    setSettings(prev => ({
      ...prev,
      location: {
        ...prev.location,
        [key]: value
      }
    }));
    toast.success('Location setting updated');
  }, [settings]);

  const updateNotificationSetting = useCallback((id: string, enabled: boolean) => {
    setSettings(prev => ({
      ...prev,
      notifications: prev.notifications.map(notification =>
        notification.id === id ? { ...notification, enabled } : notification
      )
    }));
    toast.success('Notification setting updated');
  }, []);

  const getPrivacyOptionIcon = (value: string) => {
    const option = privacyOptions.find(opt => opt.value === value);
    return option?.icon || <Users className="w-4 h-4" />;
  };



  const renderPrivacySelect = (value: string, onChange: (value: string) => void, label: string, description: string) => (
    <div className="space-y-2">
      <Label className="text-sm font-medium">{label}</Label>
      <p className="text-xs text-gray-600">{description}</p>
      <Select value={value} onValueChange={onChange}>
        <SelectTrigger className="w-full">
          <div className="flex items-center space-x-2">
            {getPrivacyOptionIcon(value)}
            <SelectValue />
          </div>
        </SelectTrigger>
        <SelectContent>
          {privacyOptions.map(option => (
            <SelectItem key={option.id} value={option.value}>
              <div className="flex items-center space-x-2">
                {option.icon}
                <div>
                  <div className="font-medium">{option.title}</div>
                  <div className="text-xs text-gray-500">{option.description}</div>
                </div>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );

  const groupedNotifications = settings.notifications.reduce((acc, notification) => {
    if (!acc[notification.category]) {
      acc[notification.category] = [];
    }
    acc[notification.category].push(notification);
    return acc;
  }, {} as Record<string, NotificationSetting[]>);

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'posts': return <Heart className="w-4 h-4" />;
      case 'messages': return <MessageCircle className="w-4 h-4" />;
      case 'friends': return <Users className="w-4 h-4" />;
      case 'pages': return <Camera className="w-4 h-4" />;
      case 'events': return <Bell className="w-4 h-4" />;
      case 'security': return <Shield className="w-4 h-4" />;
      default: return <Bell className="w-4 h-4" />;
    }
  };

  const getCategoryTitle = (category: string) => {
    switch (category) {
      case 'posts': return 'Posts & Reactions';
      case 'messages': return 'Messages';
      case 'friends': return 'Friends';
      case 'pages': return 'Pages & Content';
      case 'events': return 'Events';
      case 'security': return 'Security';
      default: return category;
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold flex items-center space-x-2">
          <Shield className="w-6 h-6 text-blue-500" />
          <span>Privacy Settings</span>
        </h1>
        <p className="text-gray-600 mt-2">
          Control who can see your information and how you're contacted on Facebook.
        </p>
      </div>

      <Tabs defaultValue="posts" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="posts">Posts</TabsTrigger>
          <TabsTrigger value="timeline">Timeline</TabsTrigger>
          <TabsTrigger value="profile">Profile</TabsTrigger>
          <TabsTrigger value="location">Location</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
        </TabsList>

        <TabsContent value="posts">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Eye className="w-5 h-5" />
                  <span>Posts & Content</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {renderPrivacySelect(
                  settings.posts.whoCanSeeMyPosts,
                  (value) => updatePostsSetting('whoCanSeeMyPosts', value),
                  'Who can see your posts?',
                  'Control who can see the posts you share on your timeline'
                )}

                <Separator />

                {renderPrivacySelect(
                  settings.posts.whoCanSeeMyFriendsList,
                  (value) => updatePostsSetting('whoCanSeeMyFriendsList', value),
                  'Who can see your friends list?',
                  'Control who can see your complete friends list'
                )}

                <Separator />

                {renderPrivacySelect(
                  settings.posts.whoCanLookMeUp,
                  (value) => updatePostsSetting('whoCanLookMeUp', value),
                  'Who can look you up using your email or phone number?',
                  'Control who can find you using your contact information'
                )}

                <Separator />

                {renderPrivacySelect(
                  settings.posts.whoCanSendMeFriendRequests,
                  (value) => updatePostsSetting('whoCanSendMeFriendRequests', value),
                  'Who can send you friend requests?',
                  'Control who can send you friend requests'
                )}
              </CardContent>
            </Card>
          </motion.div>
        </TabsContent>

        <TabsContent value="timeline">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Users className="w-5 h-5" />
                  <span>Timeline & Tagging</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {renderPrivacySelect(
                  settings.timeline.whoCanPostOnMyTimeline,
                  (value) => updateTimelineSetting('whoCanPostOnMyTimeline', value),
                  'Who can post on your timeline?',
                  'Control who can post directly to your timeline'
                )}

                <Separator />

                {renderPrivacySelect(
                  settings.timeline.whoCanSeePostsOnMyTimeline,
                  (value) => updateTimelineSetting('whoCanSeePostsOnMyTimeline', value),
                  'Who can see posts on your timeline?',
                  'Control who can see posts that others share to your timeline'
                )}

                <Separator />

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">Review posts before they appear on your timeline</Label>
                      <p className="text-xs text-gray-600">When people post on your timeline, you can review it before it appears</p>
                    </div>
                    <Switch
                      checked={settings.timeline.reviewPostsBeforeShowing}
                      onCheckedChange={(checked) => updateTimelineSetting('reviewPostsBeforeShowing', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">Review tags before they appear on your timeline</Label>
                      <p className="text-xs text-gray-600">When people tag you, you can review it before it appears on your timeline</p>
                    </div>
                    <Switch
                      checked={settings.timeline.reviewTagsBeforeShowing}
                      onCheckedChange={(checked) => updateTimelineSetting('reviewTagsBeforeShowing', checked)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </TabsContent>

        <TabsContent value="profile">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <UserCheck className="w-5 h-5" />
                  <span>Profile Information</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {renderPrivacySelect(
                  settings.profile.whoCanSeeMyEmail,
                  (value) => updateProfileSetting('whoCanSeeMyEmail', value),
                  'Who can see your email address?',
                  'Control who can see your email address on your profile'
                )}

                <Separator />

                {renderPrivacySelect(
                  settings.profile.whoCanSeeMyPhoneNumber,
                  (value) => updateProfileSetting('whoCanSeeMyPhoneNumber', value),
                  'Who can see your phone number?',
                  'Control who can see your phone number on your profile'
                )}

                <Separator />

                {renderPrivacySelect(
                  settings.profile.whoCanSeeMyBirthday,
                  (value) => updateProfileSetting('whoCanSeeMyBirthday', value),
                  'Who can see your birthday?',
                  'Control who can see your birthday on your profile'
                )}

                <Separator />

                {renderPrivacySelect(
                  settings.profile.whoCanSeeMyRelationshipStatus,
                  (value) => updateProfileSetting('whoCanSeeMyRelationshipStatus', value),
                  'Who can see your relationship status?',
                  'Control who can see your relationship status on your profile'
                )}
              </CardContent>
            </Card>
          </motion.div>
        </TabsContent>

        <TabsContent value="location">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <MapPin className="w-5 h-5" />
                  <span>Location Services</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm font-medium">Location Services</Label>
                    <p className="text-xs text-gray-600">Allow Facebook to access your location for better experiences</p>
                  </div>
                  <Switch
                    checked={settings.location.locationServices}
                    onCheckedChange={(checked) => updateLocationSetting('locationServices', checked)}
                  />
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm font-medium">Share location in posts</Label>
                    <p className="text-xs text-gray-600">Automatically add your location to posts when you share them</p>
                  </div>
                  <Switch
                    checked={settings.location.shareLocationInPosts}
                    onCheckedChange={(checked) => updateLocationSetting('shareLocationInPosts', checked)}
                  />
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm font-medium">Nearby Friends</Label>
                    <p className="text-xs text-gray-600">Let friends see when you're nearby and see when they're nearby</p>
                  </div>
                  <Switch
                    checked={settings.location.nearbyFriends}
                    onCheckedChange={(checked) => updateLocationSetting('nearbyFriends', checked)}
                  />
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </TabsContent>

        <TabsContent value="notifications">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="space-y-6"
          >
            {Object.entries(groupedNotifications).map(([category, notifications]) => (
              <Card key={category}>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    {getCategoryIcon(category)}
                    <span>{getCategoryTitle(category)}</span>
                    <Badge variant="secondary">{notifications.length}</Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {notifications.map((notification, index) => (
                    <div key={notification.id}>
                      <div className="flex items-center justify-between">
                        <div>
                          <Label className="text-sm font-medium">{notification.title}</Label>
                          <p className="text-xs text-gray-600">{notification.description}</p>
                        </div>
                        <Switch
                          checked={notification.enabled}
                          onCheckedChange={(checked) => updateNotificationSetting(notification.id, checked)}
                        />
                      </div>
                      {index < notifications.length - 1 && <Separator className="mt-4" />}
                    </div>
                  ))}
                </CardContent>
              </Card>
            ))}
          </motion.div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default PrivacySettingsComponent;