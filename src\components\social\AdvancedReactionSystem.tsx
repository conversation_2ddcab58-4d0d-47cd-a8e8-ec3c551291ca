import React, { useState, useCallback, memo, useMemo } from 'react';
import { Heart, ThumbsUp, Laugh, Angry, Sad, Surprise, Plus, Sparkles } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'sonner';

interface Reaction {
  id: string;
  emoji: string;
  name: string;
  color: string;
  animation?: string;
  sound?: string;
}

interface UserReaction {
  userId: string;
  userName: string;
  userAvatar: string;
  reaction: Reaction;
  timestamp: Date;
}

interface ReactionSummary {
  reaction: Reaction;
  count: number;
  users: UserReaction[];
}

interface AdvancedReactionSystemProps {
  postId: string;
  currentUserId: string;
  initialReactions?: UserReaction[];
  onReactionChange?: (postId: string, reactions: UserReaction[]) => void;
  showReactionDetails?: boolean;
  allowCustomReactions?: boolean;
  maxReactionsToShow?: number;
}

const AdvancedReactionSystem: React.FC<AdvancedReactionSystemProps> = memo(({
  postId,
  currentUserId,
  initialReactions = [],
  onReactionChange,
  showReactionDetails = true,
  allowCustomReactions = true,
  maxReactionsToShow = 6
}) => {
  const [reactions, setReactions] = useState<UserReaction[]>(initialReactions);
  const [showReactionPicker, setShowReactionPicker] = useState(false);
  const [showReactionDetails, setShowReactionDetailsModal] = useState(false);
  const [customReactions, setCustomReactions] = useState<Reaction[]>([]);

  // Default reaction types
  const defaultReactions: Reaction[] = useMemo(() => [
    {
      id: 'like',
      emoji: '👍',
      name: 'Like',
      color: '#1877f2',
      animation: 'bounce'
    },
    {
      id: 'love',
      emoji: '❤️',
      name: 'Love',
      color: '#e91e63',
      animation: 'pulse'
    },
    {
      id: 'laugh',
      emoji: '😂',
      name: 'Haha',
      color: '#f7b928',
      animation: 'shake'
    },
    {
      id: 'wow',
      emoji: '😮',
      name: 'Wow',
      color: '#f7b928',
      animation: 'tada'
    },
    {
      id: 'sad',
      emoji: '😢',
      name: 'Sad',
      color: '#f7b928',
      animation: 'swing'
    },
    {
      id: 'angry',
      emoji: '😡',
      name: 'Angry',
      color: '#e74c3c',
      animation: 'shake'
    },
    {
      id: 'care',
      emoji: '🤗',
      name: 'Care',
      color: '#f7b928',
      animation: 'heartBeat'
    }
  ], []);

  const allReactions = useMemo(() => [...defaultReactions, ...customReactions], [defaultReactions, customReactions]);

  // Group reactions by type and count
  const reactionSummary = useMemo(() => {
    const summary = new Map<string, ReactionSummary>();
    
    reactions.forEach(userReaction => {
      const key = userReaction.reaction.id;
      if (summary.has(key)) {
        const existing = summary.get(key)!;
        existing.count++;
        existing.users.push(userReaction);
      } else {
        summary.set(key, {
          reaction: userReaction.reaction,
          count: 1,
          users: [userReaction]
        });
      }
    });
    
    return Array.from(summary.values()).sort((a, b) => b.count - a.count);
  }, [reactions]);

  // Get current user's reaction
  const currentUserReaction = useMemo(() => {
    return reactions.find(r => r.userId === currentUserId);
  }, [reactions, currentUserId]);

  const handleReaction = useCallback((reaction: Reaction) => {
    setReactions(prev => {
      let newReactions = [...prev];
      
      // Remove existing reaction from current user
      newReactions = newReactions.filter(r => r.userId !== currentUserId);
      
      // Add new reaction if different from current or if no current reaction
      if (!currentUserReaction || currentUserReaction.reaction.id !== reaction.id) {
        const newReaction: UserReaction = {
          userId: currentUserId,
          userName: 'You',
          userAvatar: 'https://images.pexels.com/photos/614810/pexels-photo-614810.jpeg?w=100&h=100&fit=crop&crop=face',
          reaction,
          timestamp: new Date()
        };
        newReactions.push(newReaction);
        
        toast.success(`Reacted with ${reaction.emoji} ${reaction.name}`);
      } else {
        toast.success('Reaction removed');
      }
      
      onReactionChange?.(postId, newReactions);
      return newReactions;
    });
    
    setShowReactionPicker(false);
  }, [currentUserId, currentUserReaction, postId, onReactionChange]);

  const handleCreateCustomReaction = useCallback((emoji: string, name: string) => {
    const customReaction: Reaction = {
      id: `custom-${Date.now()}`,
      emoji,
      name,
      color: '#6b7280',
      animation: 'bounce'
    };
    
    setCustomReactions(prev => [...prev, customReaction]);
    toast.success(`Custom reaction "${name}" created!`);
  }, []);

  const totalReactions = reactions.length;

  return (
    <div className="space-y-2">
      {/* Reaction Summary */}
      {reactionSummary.length > 0 && (
        <div className="flex items-center space-x-2">
          <div className="flex -space-x-1">
            {reactionSummary.slice(0, maxReactionsToShow).map((summary) => (
              <motion.div
                key={summary.reaction.id}
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                className="relative"
              >
                <div
                  className="w-6 h-6 rounded-full flex items-center justify-center text-sm border-2 border-white dark:border-gray-800"
                  style={{ backgroundColor: summary.reaction.color }}
                >
                  {summary.reaction.emoji}
                </div>
                <Badge
                  variant="secondary"
                  className="absolute -top-2 -right-2 text-xs w-4 h-4 p-0 flex items-center justify-center"
                >
                  {summary.count}
                </Badge>
              </motion.div>
            ))}
          </div>
          
          {showReactionDetails && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowReactionDetailsModal(true)}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              {totalReactions} {totalReactions === 1 ? 'reaction' : 'reactions'}
            </Button>
          )}
        </div>
      )}

      {/* Reaction Button */}
      <Popover open={showReactionPicker} onOpenChange={setShowReactionPicker}>
        <PopoverTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className={`flex items-center space-x-2 ${
              currentUserReaction 
                ? 'text-blue-600 bg-blue-50 hover:bg-blue-100 dark:bg-blue-900/20 dark:text-blue-400' 
                : 'text-gray-600 hover:text-blue-600 dark:text-gray-300'
            }`}
          >
            {currentUserReaction ? (
              <>
                <span className="text-lg">{currentUserReaction.reaction.emoji}</span>
                <span className="font-medium">{currentUserReaction.reaction.name}</span>
              </>
            ) : (
              <>
                <ThumbsUp className="w-4 h-4" />
                <span className="font-medium">React</span>
              </>
            )}
          </Button>
        </PopoverTrigger>
        
        <PopoverContent className="w-auto p-2" align="start">
          <div className="flex items-center space-x-1">
            {allReactions.map((reaction) => (
              <motion.button
                key={reaction.id}
                whileHover={{ scale: 1.2 }}
                whileTap={{ scale: 0.9 }}
                onClick={() => handleReaction(reaction)}
                className="w-12 h-12 rounded-full flex items-center justify-center text-2xl hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                title={reaction.name}
              >
                {reaction.emoji}
              </motion.button>
            ))}
            
            {allowCustomReactions && (
              <Button
                variant="ghost"
                size="sm"
                className="w-12 h-12 rounded-full"
                onClick={() => {
                  // This would open a custom reaction creator
                  const emoji = prompt('Enter emoji:');
                  const name = prompt('Enter name:');
                  if (emoji && name) {
                    handleCreateCustomReaction(emoji, name);
                  }
                }}
              >
                <Plus className="w-4 h-4" />
              </Button>
            )}
          </div>
        </PopoverContent>
      </Popover>

      {/* Reaction Details Modal */}
      <AnimatePresence>
        {showReactionDetails && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setShowReactionDetailsModal(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="w-full max-w-md bg-white dark:bg-gray-800 rounded-lg overflow-hidden"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Reactions ({totalReactions})
                </h3>
              </div>
              
              <div className="max-h-96 overflow-y-auto">
                {reactionSummary.map((summary) => (
                  <div key={summary.reaction.id} className="p-4 border-b border-gray-100 dark:border-gray-700 last:border-b-0">
                    <div className="flex items-center space-x-3 mb-3">
                      <div
                        className="w-8 h-8 rounded-full flex items-center justify-center text-lg"
                        style={{ backgroundColor: summary.reaction.color }}
                      >
                        {summary.reaction.emoji}
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900 dark:text-white">
                          {summary.reaction.name}
                        </h4>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {summary.count} {summary.count === 1 ? 'person' : 'people'}
                        </p>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      {summary.users.slice(0, 5).map((user) => (
                        <div key={`${user.userId}-${user.timestamp.getTime()}`} className="flex items-center space-x-2">
                          <Avatar className="w-6 h-6">
                            <AvatarImage src={user.userAvatar} />
                            <AvatarFallback className="text-xs">
                              {user.userName.charAt(0)}
                            </AvatarFallback>
                          </Avatar>
                          <span className="text-sm text-gray-700 dark:text-gray-300">
                            {user.userName}
                          </span>
                        </div>
                      ))}
                      
                      {summary.users.length > 5 && (
                        <p className="text-sm text-gray-500 dark:text-gray-400 ml-8">
                          and {summary.users.length - 5} others
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
});

AdvancedReactionSystem.displayName = 'AdvancedReactionSystem';

export default AdvancedReactionSystem;
