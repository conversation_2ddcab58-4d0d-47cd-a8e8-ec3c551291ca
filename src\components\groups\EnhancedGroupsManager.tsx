import React, { useState, useEffect, useCallback, memo } from 'react';
import { Users, Plus, Search, Settings, Crown, Shield, UserCheck, MessageCircle, Calendar, FileText, Image, Video } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { ScrollArea } from '@/components/ui/scroll-area';
import { formatDistanceToNow } from 'date-fns';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'sonner';
import { storage } from '@/lib/storage';

interface GroupMember {
  id: string;
  name: string;
  avatar: string;
  role: 'admin' | 'moderator' | 'member';
  joinedAt: Date;
  isOnline: boolean;
  lastActive?: Date;
}

interface GroupPost {
  id: string;
  authorId: string;
  content: string;
  images?: string[];
  timestamp: Date;
  likes: number;
  comments: number;
  isPinned?: boolean;
}

interface Group {
  id: string;
  name: string;
  description: string;
  avatar: string;
  coverImage: string;
  privacy: 'public' | 'private' | 'secret';
  memberCount: number;
  members: GroupMember[];
  posts: GroupPost[];
  tags: string[];
  rules: string[];
  createdAt: Date;
  isJoined: boolean;
  userRole?: 'admin' | 'moderator' | 'member';
  pendingRequests?: number;
  events?: number;
  files?: number;
}

interface EnhancedGroupsManagerProps {
  currentUserId: string;
  onGroupSelect?: (group: Group) => void;
}

const EnhancedGroupsManager: React.FC<EnhancedGroupsManagerProps> = memo(({
  currentUserId,
  onGroupSelect
}) => {
  const [groups, setGroups] = useState<Group[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<'all' | 'joined' | 'managed' | 'suggested'>('all');
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState<Group | null>(null);
  const [activeTab, setActiveTab] = useState('posts');
  const [isLoading, setIsLoading] = useState(false);

  // New group form state
  const [newGroup, setNewGroup] = useState({
    name: '',
    description: '',
    privacy: 'public' as 'public' | 'private' | 'secret',
    tags: [] as string[],
    rules: [] as string[]
  });

  // Load groups from storage or initialize with mock data
  useEffect(() => {
    const savedGroups = storage.get<Group[]>('user_groups', []);
    if (savedGroups.length > 0) {
      setGroups(savedGroups.map(g => ({
        ...g,
        createdAt: new Date(g.createdAt),
        members: g.members.map(m => ({
          ...m,
          joinedAt: new Date(m.joinedAt),
          lastActive: m.lastActive ? new Date(m.lastActive) : undefined
        })),
        posts: g.posts.map(p => ({
          ...p,
          timestamp: new Date(p.timestamp)
        }))
      })));
    } else {
      // Initialize with mock groups
      const mockGroups: Group[] = [
        {
          id: '1',
          name: 'React Developers',
          description: 'A community for React developers to share knowledge and collaborate',
          avatar: 'https://images.pexels.com/photos/3182812/pexels-photo-3182812.jpeg?w=400&h=400&fit=crop',
          coverImage: 'https://images.pexels.com/photos/1640777/pexels-photo-1640777.jpeg?w=1200&h=400&fit=crop',
          privacy: 'public',
          memberCount: 1250,
          members: [
            {
              id: currentUserId,
              name: 'You',
              avatar: 'https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg?w=400&h=400&fit=crop&crop=face',
              role: 'admin',
              joinedAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
              isOnline: true
            },
            {
              id: '2',
              name: 'Sarah Johnson',
              avatar: 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?w=400&h=400&fit=crop&crop=face',
              role: 'moderator',
              joinedAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
              isOnline: true
            }
          ],
          posts: [
            {
              id: '1',
              authorId: '2',
              content: 'Just released a new React hook for state management! Check it out and let me know what you think.',
              images: ['https://images.pexels.com/photos/1640777/pexels-photo-1640777.jpeg?w=800&h=600&fit=crop'],
              timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
              likes: 45,
              comments: 12,
              isPinned: true
            }
          ],
          tags: ['react', 'javascript', 'frontend', 'development'],
          rules: [
            'Be respectful to all members',
            'No spam or self-promotion without permission',
            'Keep discussions relevant to React development',
            'Use appropriate channels for different topics'
          ],
          createdAt: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000),
          isJoined: true,
          userRole: 'admin',
          pendingRequests: 5,
          events: 3,
          files: 25
        },
        {
          id: '2',
          name: 'Photography Enthusiasts',
          description: 'Share your best shots and learn from fellow photographers',
          avatar: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?w=400&h=400&fit=crop',
          coverImage: 'https://images.pexels.com/photos/1640770/pexels-photo-1640770.jpeg?w=1200&h=400&fit=crop',
          privacy: 'public',
          memberCount: 890,
          members: [],
          posts: [],
          tags: ['photography', 'art', 'creative'],
          rules: ['Original content only', 'Constructive feedback encouraged'],
          createdAt: new Date(Date.now() - 200 * 24 * 60 * 60 * 1000),
          isJoined: true,
          userRole: 'member',
          events: 1,
          files: 150
        },
        {
          id: '3',
          name: 'Local Food Lovers',
          description: 'Discover the best local restaurants and share food experiences',
          avatar: 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?w=400&h=400&fit=crop',
          coverImage: 'https://images.pexels.com/photos/1640771/pexels-photo-1640771.jpeg?w=1200&h=400&fit=crop',
          privacy: 'private',
          memberCount: 156,
          members: [],
          posts: [],
          tags: ['food', 'local', 'restaurants'],
          rules: ['No commercial promotion', 'Include location in posts'],
          createdAt: new Date(Date.now() - 100 * 24 * 60 * 60 * 1000),
          isJoined: false,
          events: 2,
          files: 45
        }
      ];
      setGroups(mockGroups);
    }
  }, [currentUserId]);

  // Save groups to storage when they change
  useEffect(() => {
    if (groups.length > 0) {
      storage.set('user_groups', groups);
    }
  }, [groups]);

  const handleCreateGroup = useCallback(async () => {
    if (!newGroup.name.trim() || !newGroup.description.trim()) {
      toast.error('Please fill in all required fields');
      return;
    }

    setIsLoading(true);

    // Simulate API call
    setTimeout(() => {
      const group: Group = {
        id: Date.now().toString(),
        name: newGroup.name,
        description: newGroup.description,
        avatar: 'https://images.pexels.com/photos/3182812/pexels-photo-3182812.jpeg?w=400&h=400&fit=crop',
        coverImage: 'https://images.pexels.com/photos/1640777/pexels-photo-1640777.jpeg?w=1200&h=400&fit=crop',
        privacy: newGroup.privacy,
        memberCount: 1,
        members: [{
          id: currentUserId,
          name: 'You',
          avatar: 'https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg?w=400&h=400&fit=crop&crop=face',
          role: 'admin',
          joinedAt: new Date(),
          isOnline: true
        }],
        posts: [],
        tags: newGroup.tags,
        rules: newGroup.rules,
        createdAt: new Date(),
        isJoined: true,
        userRole: 'admin',
        pendingRequests: 0,
        events: 0,
        files: 0
      };

      setGroups(prev => [group, ...prev]);
      setShowCreateDialog(false);
      setNewGroup({
        name: '',
        description: '',
        privacy: 'public',
        tags: [],
        rules: []
      });
      setIsLoading(false);
      toast.success('Group created successfully!');
    }, 1000);
  }, [newGroup, currentUserId]);

  const handleJoinGroup = useCallback((groupId: string) => {
    setGroups(prev => prev.map(group =>
      group.id === groupId
        ? {
            ...group,
            isJoined: true,
            userRole: 'member',
            memberCount: group.memberCount + 1,
            members: [...group.members, {
              id: currentUserId,
              name: 'You',
              avatar: 'https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg?w=400&h=400&fit=crop&crop=face',
              role: 'member',
              joinedAt: new Date(),
              isOnline: true
            }]
          }
        : group
    ));
    toast.success('Successfully joined the group!');
  }, [currentUserId]);

  const handleLeaveGroup = useCallback((groupId: string) => {
    setGroups(prev => prev.map(group =>
      group.id === groupId
        ? {
            ...group,
            isJoined: false,
            userRole: undefined,
            memberCount: Math.max(0, group.memberCount - 1),
            members: group.members.filter(m => m.id !== currentUserId)
          }
        : group
    ));
    toast.success('Left the group');
  }, [currentUserId]);

  const getRoleIcon = (role: GroupMember['role']) => {
    switch (role) {
      case 'admin':
        return <Crown className="w-3 h-3 text-yellow-500" />;
      case 'moderator':
        return <Shield className="w-3 h-3 text-blue-500" />;
      default:
        return <UserCheck className="w-3 h-3 text-green-500" />;
    }
  };

  const getPrivacyBadge = (privacy: Group['privacy']) => {
    const variants = {
      public: 'default',
      private: 'secondary',
      secret: 'destructive'
    } as const;

    return (
      <Badge variant={variants[privacy]} className="text-xs">
        {privacy.charAt(0).toUpperCase() + privacy.slice(1)}
      </Badge>
    );
  };

  const filteredGroups = groups.filter(group => {
    const matchesSearch = group.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         group.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         group.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));

    switch (selectedCategory) {
      case 'joined':
        return matchesSearch && group.isJoined;
      case 'managed':
        return matchesSearch && group.isJoined && (group.userRole === 'admin' || group.userRole === 'moderator');
      case 'suggested':
        return matchesSearch && !group.isJoined;
      default:
        return matchesSearch;
    }
  });

  return (
    <div className="w-full max-w-6xl mx-auto p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Groups</h1>
          <p className="text-gray-600 dark:text-gray-300">Discover and manage your communities</p>
        </div>
        <Button onClick={() => setShowCreateDialog(true)} className="bg-blue-500 hover:bg-blue-600">
          <Plus className="w-4 h-4 mr-2" />
          Create Group
        </Button>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Search groups..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={selectedCategory} onValueChange={(value: any) => setSelectedCategory(value)}>
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Groups</SelectItem>
            <SelectItem value="joined">Joined</SelectItem>
            <SelectItem value="managed">Managed</SelectItem>
            <SelectItem value="suggested">Suggested</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Groups Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <AnimatePresence>
          {filteredGroups.map((group) => (
            <motion.div
              key={group.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="group"
            >
              <Card className="overflow-hidden hover:shadow-lg transition-shadow cursor-pointer">
                <div
                  className="relative h-32 bg-cover bg-center"
                  style={{ backgroundImage: `url(${group.coverImage})` }}
                  onClick={() => {
                    setSelectedGroup(group);
                    onGroupSelect?.(group);
                  }}
                >
                  <div className="absolute inset-0 bg-black bg-opacity-40" />
                  <div className="absolute top-3 right-3">
                    {getPrivacyBadge(group.privacy)}
                  </div>
                  <div className="absolute bottom-3 left-3">
                    <Avatar className="w-12 h-12 border-2 border-white">
                      <AvatarImage src={group.avatar} />
                      <AvatarFallback>{group.name.charAt(0)}</AvatarFallback>
                    </Avatar>
                  </div>
                </div>

                <CardContent className="p-4">
                  <div className="flex items-start justify-between mb-2">
                    <h3 className="font-semibold text-gray-900 dark:text-white truncate">
                      {group.name}
                    </h3>
                    {group.userRole && (
                      <div className="ml-2">
                        {getRoleIcon(group.userRole)}
                      </div>
                    )}
                  </div>

                  <p className="text-sm text-gray-600 dark:text-gray-300 mb-3 line-clamp-2">
                    {group.description}
                  </p>

                  <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-3">
                    <span className="flex items-center">
                      <Users className="w-4 h-4 mr-1" />
                      {group.memberCount.toLocaleString()} members
                    </span>
                    <span>{formatDistanceToNow(group.createdAt, { addSuffix: true })}</span>
                  </div>

                  <div className="flex flex-wrap gap-1 mb-3">
                    {group.tags.slice(0, 3).map((tag) => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                    {group.tags.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{group.tags.length - 3}
                      </Badge>
                    )}
                  </div>

                  <div className="flex items-center justify-between">
                    {group.isJoined ? (
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setSelectedGroup(group)}
                          className="flex-1"
                        >
                          <MessageCircle className="w-3 h-3 mr-1" />
                          View
                        </Button>
                        {(group.userRole === 'admin' || group.userRole === 'moderator') && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setSelectedGroup(group)}
                          >
                            <Settings className="w-3 h-3" />
                          </Button>
                        )}
                      </div>
                    ) : (
                      <Button
                        onClick={() => handleJoinGroup(group.id)}
                        className="w-full bg-blue-500 hover:bg-blue-600"
                        size="sm"
                      >
                        Join Group
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {filteredGroups.length === 0 && (
        <div className="text-center py-12">
          <Users className="w-16 h-16 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No groups found
          </h3>
          <p className="text-gray-500 dark:text-gray-400 mb-4">
            {searchQuery ? 'Try adjusting your search terms' : 'Create your first group to get started'}
          </p>
          {!searchQuery && (
            <Button onClick={() => setShowCreateDialog(true)} className="bg-blue-500 hover:bg-blue-600">
              <Plus className="w-4 h-4 mr-2" />
              Create Group
            </Button>
          )}
        </div>
      )}

      {/* Create Group Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Create New Group</DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="groupName">Group Name *</Label>
              <Input
                id="groupName"
                placeholder="Enter group name"
                value={newGroup.name}
                onChange={(e) => setNewGroup(prev => ({ ...prev, name: e.target.value }))}
              />
            </div>

            <div>
              <Label htmlFor="groupDescription">Description *</Label>
              <Textarea
                id="groupDescription"
                placeholder="Describe your group"
                value={newGroup.description}
                onChange={(e) => setNewGroup(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
              />
            </div>

            <div>
              <Label htmlFor="groupPrivacy">Privacy</Label>
              <Select
                value={newGroup.privacy}
                onValueChange={(value: 'public' | 'private' | 'secret') =>
                  setNewGroup(prev => ({ ...prev, privacy: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="public">Public - Anyone can join</SelectItem>
                  <SelectItem value="private">Private - Approval required</SelectItem>
                  <SelectItem value="secret">Secret - Invite only</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => setShowCreateDialog(false)}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button
                onClick={handleCreateGroup}
                disabled={isLoading || !newGroup.name.trim() || !newGroup.description.trim()}
                className="bg-blue-500 hover:bg-blue-600"
              >
                {isLoading ? 'Creating...' : 'Create Group'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
});

EnhancedGroupsManager.displayName = 'EnhancedGroupsManager';

export default EnhancedGroupsManager;