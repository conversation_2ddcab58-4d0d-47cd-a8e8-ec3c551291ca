import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { 
  PerformanceMonitor, 
  BundleAnalyzer,
  getMemoryUsage,
  getNetworkInfo,
  shouldLoadHighQuality,
  getBundleSplittingRecommendations,
  checkPerformanceBudget
} from '@/utils/bundleOptimization';

// Mock performance APIs
const mockPerformanceObserver = vi.fn();
const mockPerformanceTiming = {
  navigationStart: 1000,
  responseStart: 1200,
  loadEventEnd: 2000
};

const mockPerformanceMemory = {
  usedJSHeapSize: 50000000,
  totalJSHeapSize: 100000000,
  jsHeapSizeLimit: 200000000
};

const mockConnection = {
  effectiveType: '4g',
  downlink: 10,
  rtt: 50,
  saveData: false
};

// Setup global mocks
beforeEach(() => {
  global.PerformanceObserver = mockPerformanceObserver;
  global.performance = {
    timing: mockPerformanceTiming,
    memory: mockPerformanceMemory,
    now: vi.fn(() => Date.now())
  } as any;
  
  Object.defineProperty(global.navigator, 'connection', {
    value: mockConnection,
    writable: true
  });

  vi.clearAllMocks();
});

afterEach(() => {
  vi.restoreAllMocks();
});

describe('PerformanceMonitor', () => {
  it('initializes with default metrics', () => {
    const monitor = new PerformanceMonitor();
    const metrics = monitor.getMetrics();
    
    expect(metrics).toEqual({
      fcp: 0,
      lcp: 0,
      fid: 0,
      cls: 0,
      ttfb: 0
    });
  });

  it('calculates performance score correctly', () => {
    const monitor = new PerformanceMonitor();
    
    // Test with good metrics
    monitor['metrics'] = {
      fcp: 1500,  // Good: < 1800ms
      lcp: 2000,  // Good: < 2500ms
      fid: 50,    // Good: < 100ms
      cls: 0.05,  // Good: < 0.1
      ttfb: 200
    };
    
    const score = monitor.getScore();
    expect(score).toBe(100); // Perfect score
  });

  it('penalizes poor performance metrics', () => {
    const monitor = new PerformanceMonitor();
    
    // Test with poor metrics
    monitor['metrics'] = {
      fcp: 4000,  // Poor: > 3000ms (-20)
      lcp: 5000,  // Poor: > 4000ms (-30)
      fid: 400,   // Poor: > 300ms (-25)
      cls: 0.3,   // Poor: > 0.25 (-25)
      ttfb: 1000
    };
    
    const score = monitor.getScore();
    expect(score).toBe(0); // 100 - 20 - 30 - 25 - 25 = 0
  });

  it('handles missing PerformanceObserver gracefully', () => {
    global.PerformanceObserver = undefined as any;
    
    expect(() => new PerformanceMonitor()).not.toThrow();
  });

  it('disconnects observers properly', () => {
    const mockDisconnect = vi.fn();
    const mockObserver = {
      observe: vi.fn(),
      disconnect: mockDisconnect
    };
    
    global.PerformanceObserver = vi.fn(() => mockObserver) as any;
    
    const monitor = new PerformanceMonitor();
    monitor.disconnect();
    
    expect(mockDisconnect).toHaveBeenCalled();
  });
});

describe('BundleAnalyzer', () => {
  let analyzer: BundleAnalyzer;

  beforeEach(() => {
    analyzer = BundleAnalyzer.getInstance();
    // Clear any existing chunks
    analyzer['chunks'].clear();
  });

  it('is a singleton', () => {
    const analyzer1 = BundleAnalyzer.getInstance();
    const analyzer2 = BundleAnalyzer.getInstance();
    
    expect(analyzer1).toBe(analyzer2);
  });

  it('registers and retrieves chunks', () => {
    const chunkInfo = {
      size: 100000,
      gzippedSize: 30000,
      modules: ['module1', 'module2'],
      isAsync: true,
      priority: 'high' as const
    };
    
    analyzer.registerChunk('main', chunkInfo);
    
    const retrieved = analyzer.getChunkInfo('main');
    expect(retrieved).toEqual({ name: 'main', ...chunkInfo });
  });

  it('calculates total sizes correctly', () => {
    analyzer.registerChunk('chunk1', {
      size: 50000,
      gzippedSize: 15000,
      modules: ['module1'],
      isAsync: false,
      priority: 'high'
    });
    
    analyzer.registerChunk('chunk2', {
      size: 30000,
      gzippedSize: 10000,
      modules: ['module2'],
      isAsync: true,
      priority: 'medium'
    });
    
    expect(analyzer.getTotalSize()).toBe(80000);
    expect(analyzer.getTotalGzippedSize()).toBe(25000);
  });

  it('identifies unused chunks', () => {
    analyzer.registerChunk('used', {
      size: 50000,
      gzippedSize: 15000,
      modules: ['module1'],
      isAsync: false,
      priority: 'high'
    });
    
    analyzer.registerChunk('unused', {
      size: 30000,
      gzippedSize: 10000,
      modules: ['module2'],
      isAsync: true,
      priority: 'low'
    });
    
    const unusedChunks = analyzer.getUnusedChunks();
    expect(unusedChunks).toHaveLength(1);
    expect(unusedChunks[0].name).toBe('unused');
  });

  it('generates comprehensive report', () => {
    analyzer.registerChunk('test', {
      size: 100000,
      gzippedSize: 30000,
      modules: ['module1', 'module2'],
      isAsync: true,
      priority: 'high'
    });
    
    const report = analyzer.generateReport();
    
    expect(report).toEqual({
      totalSize: 100000,
      gzippedSize: 30000,
      chunks: expect.any(Array),
      loadTime: 0,
      renderTime: 0,
      interactiveTime: 0
    });
    
    expect(report.chunks).toHaveLength(1);
  });
});

describe('Memory and Network Utilities', () => {
  it('gets memory usage when available', () => {
    const memoryUsage = getMemoryUsage();
    
    expect(memoryUsage).toEqual({
      usedJSHeapSize: 50000000,
      totalJSHeapSize: 100000000,
      jsHeapSizeLimit: 200000000,
      usagePercentage: 25 // 50MB / 200MB * 100
    });
  });

  it('returns null when memory API unavailable', () => {
    delete (global.performance as any).memory;
    
    const memoryUsage = getMemoryUsage();
    expect(memoryUsage).toBeNull();
  });

  it('gets network information when available', () => {
    const networkInfo = getNetworkInfo();
    
    expect(networkInfo).toEqual({
      effectiveType: '4g',
      downlink: 10,
      rtt: 50,
      saveData: false
    });
  });

  it('returns null when connection API unavailable', () => {
    delete (global.navigator as any).connection;
    
    const networkInfo = getNetworkInfo();
    expect(networkInfo).toBeNull();
  });

  it('determines high quality loading based on network', () => {
    // Good connection
    expect(shouldLoadHighQuality()).toBe(true);
    
    // Slow connection
    Object.defineProperty(global.navigator, 'connection', {
      value: { ...mockConnection, effectiveType: '2g' },
      writable: true
    });
    expect(shouldLoadHighQuality()).toBe(false);
    
    // Data saver enabled
    Object.defineProperty(global.navigator, 'connection', {
      value: { ...mockConnection, saveData: true },
      writable: true
    });
    expect(shouldLoadHighQuality()).toBe(false);
  });
});

describe('Bundle Optimization Recommendations', () => {
  it('identifies large chunks', () => {
    const chunks = [
      {
        name: 'large-chunk',
        size: 300000, // 300KB - above 250KB threshold
        gzippedSize: 100000,
        modules: ['module1'],
        isAsync: false,
        priority: 'high' as const
      },
      {
        name: 'small-chunk',
        size: 50000,
        gzippedSize: 15000,
        modules: ['module2'],
        isAsync: true,
        priority: 'medium' as const
      }
    ];
    
    const recommendations = getBundleSplittingRecommendations(chunks);
    
    expect(recommendations).toContain('Consider splitting large chunks: large-chunk');
  });

  it('identifies unused chunks', () => {
    const chunks = [
      {
        name: 'used-chunk',
        size: 50000,
        gzippedSize: 15000,
        modules: ['module1'],
        isAsync: false,
        priority: 'high' as const
      },
      {
        name: 'unused-chunk',
        size: 30000,
        gzippedSize: 10000,
        modules: ['module2'],
        isAsync: true,
        priority: 'low' as const
      }
    ];
    
    const recommendations = getBundleSplittingRecommendations(chunks);
    
    expect(recommendations).toContain('Consider removing unused chunks: unused-chunk');
  });

  it('identifies duplicate modules', () => {
    const chunks = [
      {
        name: 'chunk1',
        size: 50000,
        gzippedSize: 15000,
        modules: ['shared-module', 'module1'],
        isAsync: false,
        priority: 'high' as const
      },
      {
        name: 'chunk2',
        size: 40000,
        gzippedSize: 12000,
        modules: ['shared-module', 'module2'],
        isAsync: true,
        priority: 'medium' as const
      }
    ];
    
    const recommendations = getBundleSplittingRecommendations(chunks);
    
    expect(recommendations).toContain('Consider extracting common modules: shared-module');
  });
});

describe('Performance Budget Checker', () => {
  it('identifies budget violations', () => {
    const metrics = {
      fcp: 2000,
      lcp: 3000,
      fid: 150,
      cls: 0.15,
      ttfb: 500
    };
    
    const budget = {
      fcp: 1800,
      lcp: 2500,
      fid: 100
    };
    
    const violations = checkPerformanceBudget(metrics, budget);
    
    expect(violations).toContain('fcp: 2000ms exceeds budget of 1800ms');
    expect(violations).toContain('lcp: 3000ms exceeds budget of 2500ms');
    expect(violations).toContain('fid: 150ms exceeds budget of 100ms');
  });

  it('returns empty array when within budget', () => {
    const metrics = {
      fcp: 1500,
      lcp: 2000,
      fid: 50,
      cls: 0.05,
      ttfb: 200
    };
    
    const budget = {
      fcp: 1800,
      lcp: 2500,
      fid: 100
    };
    
    const violations = checkPerformanceBudget(metrics, budget);
    
    expect(violations).toHaveLength(0);
  });
});

describe('Performance Integration Tests', () => {
  it('monitors and analyzes performance together', () => {
    const monitor = new PerformanceMonitor();
    const analyzer = BundleAnalyzer.getInstance();
    
    // Set up some performance data
    monitor['metrics'] = {
      fcp: 1500,
      lcp: 2000,
      fid: 50,
      cls: 0.05,
      ttfb: 200
    };
    
    analyzer.registerChunk('main', {
      size: 150000,
      gzippedSize: 50000,
      modules: ['app', 'vendor'],
      isAsync: false,
      priority: 'high'
    });
    
    const performanceScore = monitor.getScore();
    const bundleReport = analyzer.generateReport();
    
    expect(performanceScore).toBe(100);
    expect(bundleReport.totalSize).toBe(150000);
    
    // Check if performance is good enough for high quality content
    expect(shouldLoadHighQuality()).toBe(true);
  });

  it('provides actionable insights for optimization', () => {
    const analyzer = BundleAnalyzer.getInstance();
    analyzer['chunks'].clear();
    
    // Add some problematic chunks
    analyzer.registerChunk('bloated-main', {
      size: 500000, // Too large
      gzippedSize: 150000,
      modules: ['react', 'lodash', 'moment', 'app-code'],
      isAsync: false,
      priority: 'high'
    });
    
    analyzer.registerChunk('unused-feature', {
      size: 100000,
      gzippedSize: 30000,
      modules: ['unused-feature'],
      isAsync: true,
      priority: 'low' // Unused
    });
    
    const recommendations = getBundleSplittingRecommendations(analyzer.getAllChunks());
    
    expect(recommendations).toContain('Consider splitting large chunks: bloated-main');
    expect(recommendations).toContain('Consider removing unused chunks: unused-feature');
  });
});
