import React, { useState, useEffect, useRef, useCallback, memo } from 'react';
import { <PERSON>, Mic, MicOff, <PERSON>tings, Users, MessageCircle, Heart, Share2, Eye, Radio, Square } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'sonner';
import { formatDistanceToNow } from 'date-fns';

interface LiveComment {
  id: string;
  userId: string;
  userName: string;
  userAvatar: string;
  message: string;
  timestamp: Date;
  isSuperchat?: boolean;
  amount?: number;
}

interface LiveViewer {
  id: string;
  name: string;
  avatar: string;
  joinedAt: Date;
}

interface LiveStreamingStudioProps {
  isOpen: boolean;
  onClose: () => void;
  currentUserId: string;
}

const LiveStreamingStudio: React.FC<LiveStreamingStudioProps> = memo(({
  isOpen,
  onClose,
  currentUserId
}) => {
  const [isLive, setIsLive] = useState(false);
  const [isPreview, setIsPreview] = useState(false);
  const [streamTitle, setStreamTitle] = useState('');
  const [streamDescription, setStreamDescription] = useState('');
  const [viewerCount, setViewerCount] = useState(0);
  const [likeCount, setLikeCount] = useState(0);
  const [comments, setComments] = useState<LiveComment[]>([]);
  const [viewers, setViewers] = useState<LiveViewer[]>([]);
  const [newComment, setNewComment] = useState('');
  const [streamDuration, setStreamDuration] = useState(0);
  const [isAudioEnabled, setIsAudioEnabled] = useState(true);
  const [showChat, setShowChat] = useState(true);
  const [streamQuality, setStreamQuality] = useState<'720p' | '1080p' | '4K'>('1080p');
  
  const videoRef = useRef<HTMLVideoElement>(null);
  const commentsEndRef = useRef<HTMLDivElement>(null);

  // Stream duration timer
  useEffect(() => {
    if (isLive) {
      const interval = setInterval(() => {
        setStreamDuration(prev => prev + 1);
      }, 1000);
      return () => clearInterval(interval);
    }
  }, [isLive]);

  // Simulate live comments
  useEffect(() => {
    if (isLive) {
      const interval = setInterval(() => {
        if (Math.random() > 0.7) {
          const mockComments = [
            'Great stream! 🔥',
            'Love this content!',
            'Hello from Brazil! 🇧🇷',
            'First time watching, amazing!',
            'Can you show that again?',
            'This is so helpful, thank you!',
            'Subscribed! 👍',
            'What camera are you using?'
          ];
          
          const newComment: LiveComment = {
            id: Date.now().toString(),
            userId: `user-${Math.random()}`,
            userName: `Viewer${Math.floor(Math.random() * 1000)}`,
            userAvatar: `https://images.pexels.com/photos/${Math.floor(Math.random() * 1000000)}/pexels-photo.jpeg?w=100&h=100&fit=crop&crop=face`,
            message: mockComments[Math.floor(Math.random() * mockComments.length)],
            timestamp: new Date(),
            isSuperchat: Math.random() > 0.9,
            amount: Math.random() > 0.9 ? Math.floor(Math.random() * 50) + 5 : undefined
          };
          
          setComments(prev => [...prev, newComment]);
        }
      }, 3000);
      
      return () => clearInterval(interval);
    }
  }, [isLive]);

  // Simulate viewer count changes
  useEffect(() => {
    if (isLive) {
      const interval = setInterval(() => {
        setViewerCount(prev => {
          const change = Math.floor(Math.random() * 10) - 5;
          return Math.max(0, prev + change);
        });
      }, 5000);
      
      return () => clearInterval(interval);
    }
  }, [isLive]);

  // Auto-scroll comments
  useEffect(() => {
    commentsEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [comments]);

  const handleStartStream = useCallback(() => {
    if (!streamTitle.trim()) {
      toast.error('Please enter a stream title');
      return;
    }
    
    setIsLive(true);
    setIsPreview(false);
    setViewerCount(Math.floor(Math.random() * 50) + 10);
    toast.success('Live stream started!');
  }, [streamTitle]);

  const handleEndStream = useCallback(() => {
    setIsLive(false);
    setIsPreview(false);
    setStreamDuration(0);
    setComments([]);
    setViewerCount(0);
    toast.success('Live stream ended');
  }, []);

  const handleStartPreview = useCallback(() => {
    setIsPreview(true);
    toast.success('Preview started');
  }, []);

  const handleSendComment = useCallback(() => {
    if (!newComment.trim()) return;
    
    const comment: LiveComment = {
      id: Date.now().toString(),
      userId: currentUserId,
      userName: 'You',
      userAvatar: 'https://images.pexels.com/photos/614810/pexels-photo-614810.jpeg?w=100&h=100&fit=crop&crop=face',
      message: newComment,
      timestamp: new Date()
    };
    
    setComments(prev => [...prev, comment]);
    setNewComment('');
  }, [newComment, currentUserId]);

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const mins = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (!isOpen) return null;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
    >
      <div className="w-full max-w-7xl h-full max-h-[90vh] bg-white dark:bg-gray-900 rounded-lg overflow-hidden flex">
        {/* Main Stream Area */}
        <div className="flex-1 flex flex-col">
          {/* Stream Preview/Live Video */}
          <div className="flex-1 bg-black relative">
            <video
              ref={videoRef}
              className="w-full h-full object-cover"
              autoPlay
              muted
              playsInline
            />
            
            {/* Stream Status Overlay */}
            <div className="absolute top-4 left-4 right-4 flex items-center justify-between">
              <div className="flex items-center space-x-3">
                {isLive && (
                  <Badge className="bg-red-500 text-white animate-pulse">
                    <Radio className="w-3 h-3 mr-1" />
                    LIVE
                  </Badge>
                )}
                {isPreview && (
                  <Badge className="bg-blue-500 text-white">
                    PREVIEW
                  </Badge>
                )}
                {isLive && (
                  <Badge variant="secondary" className="bg-black/50 text-white">
                    {formatDuration(streamDuration)}
                  </Badge>
                )}
              </div>
              
              <div className="flex items-center space-x-2">
                {isLive && (
                  <>
                    <Badge variant="secondary" className="bg-black/50 text-white">
                      <Eye className="w-3 h-3 mr-1" />
                      {viewerCount.toLocaleString()}
                    </Badge>
                    <Badge variant="secondary" className="bg-black/50 text-white">
                      <Heart className="w-3 h-3 mr-1" />
                      {likeCount.toLocaleString()}
                    </Badge>
                  </>
                )}
              </div>
            </div>

            {/* Stream Controls */}
            <div className="absolute bottom-4 left-4 right-4">
              <div className="flex items-center justify-center space-x-4">
                <Button
                  onClick={() => setIsAudioEnabled(!isAudioEnabled)}
                  className={`w-12 h-12 rounded-full ${
                    isAudioEnabled 
                      ? 'bg-gray-700 hover:bg-gray-600' 
                      : 'bg-red-500 hover:bg-red-600'
                  }`}
                >
                  {isAudioEnabled ? (
                    <Mic className="w-5 h-5 text-white" />
                  ) : (
                    <MicOff className="w-5 h-5 text-white" />
                  )}
                </Button>

                {!isLive && !isPreview && (
                  <Button
                    onClick={handleStartPreview}
                    className="bg-blue-500 hover:bg-blue-600 text-white px-6"
                  >
                    <Video className="w-4 h-4 mr-2" />
                    Start Preview
                  </Button>
                )}

                {isPreview && !isLive && (
                  <Button
                    onClick={handleStartStream}
                    className="bg-red-500 hover:bg-red-600 text-white px-6"
                  >
                    <Radio className="w-4 h-4 mr-2" />
                    Go Live
                  </Button>
                )}

                {isLive && (
                  <Button
                    onClick={handleEndStream}
                    className="bg-red-500 hover:bg-red-600 text-white px-6"
                  >
                    <Square className="w-4 h-4 mr-2" />
                    End Stream
                  </Button>
                )}

                <Button
                  className="w-12 h-12 rounded-full bg-gray-700 hover:bg-gray-600"
                >
                  <Settings className="w-5 h-5 text-white" />
                </Button>
              </div>
            </div>
          </div>

          {/* Stream Info */}
          {!isLive && (
            <div className="p-4 border-t border-gray-200 dark:border-gray-700">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="streamTitle">Stream Title</Label>
                  <Input
                    id="streamTitle"
                    placeholder="What's your stream about?"
                    value={streamTitle}
                    onChange={(e) => setStreamTitle(e.target.value)}
                    className="mt-1"
                  />
                </div>
                
                <div>
                  <Label htmlFor="streamDescription">Description (Optional)</Label>
                  <Textarea
                    id="streamDescription"
                    placeholder="Tell viewers more about your stream..."
                    value={streamDescription}
                    onChange={(e) => setStreamDescription(e.target.value)}
                    className="mt-1"
                    rows={2}
                  />
                </div>

                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <Label htmlFor="quality">Quality:</Label>
                    <select
                      id="quality"
                      value={streamQuality}
                      onChange={(e) => setStreamQuality(e.target.value as any)}
                      className="px-3 py-1 border rounded"
                    >
                      <option value="720p">720p</option>
                      <option value="1080p">1080p</option>
                      <option value="4K">4K</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Chat Sidebar */}
        {showChat && (
          <div className="w-80 border-l border-gray-200 dark:border-gray-700 flex flex-col">
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold text-gray-900 dark:text-white">
                  Live Chat
                </h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowChat(false)}
                >
                  ×
                </Button>
              </div>
              {isLive && (
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                  {viewerCount} viewers
                </p>
              )}
            </div>

            {/* Comments */}
            <ScrollArea className="flex-1 p-4">
              <div className="space-y-3">
                <AnimatePresence>
                  {comments.map((comment) => (
                    <motion.div
                      key={comment.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      className={`flex items-start space-x-2 ${
                        comment.isSuperchat ? 'bg-yellow-50 dark:bg-yellow-900/20 p-2 rounded' : ''
                      }`}
                    >
                      <Avatar className="w-6 h-6">
                        <AvatarImage src={comment.userAvatar} />
                        <AvatarFallback className="text-xs">
                          {comment.userName.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-1">
                          <span className="text-sm font-medium text-gray-900 dark:text-white">
                            {comment.userName}
                          </span>
                          {comment.isSuperchat && (
                            <Badge variant="secondary" className="text-xs">
                              ${comment.amount}
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-gray-700 dark:text-gray-300 break-words">
                          {comment.message}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {formatDistanceToNow(comment.timestamp, { addSuffix: true })}
                        </p>
                      </div>
                    </motion.div>
                  ))}
                </AnimatePresence>
                <div ref={commentsEndRef} />
              </div>
            </ScrollArea>

            {/* Comment Input */}
            {isLive && (
              <div className="p-4 border-t border-gray-200 dark:border-gray-700">
                <div className="flex items-center space-x-2">
                  <Input
                    placeholder="Say something..."
                    value={newComment}
                    onChange={(e) => setNewComment(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSendComment()}
                    className="flex-1"
                  />
                  <Button
                    onClick={handleSendComment}
                    disabled={!newComment.trim()}
                    size="sm"
                  >
                    Send
                  </Button>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Close Button */}
        <Button
          onClick={onClose}
          variant="ghost"
          className="absolute top-4 right-4 text-white hover:bg-white/20"
        >
          ×
        </Button>
      </div>
    </motion.div>
  );
});

LiveStreamingStudio.displayName = 'LiveStreamingStudio';

export default LiveStreamingStudio;
