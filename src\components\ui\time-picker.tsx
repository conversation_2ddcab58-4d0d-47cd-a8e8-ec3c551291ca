import * as React from "react"
import { Clock } from "lucide-react"
import { cn } from "@/lib/utils"
import { Input } from "@/components/ui/input"

export interface TimePickerProps {
  time?: Date;
  onTimeChange?: (time: Date) => void;
  className?: string;
}

const TimePicker = React.forwardRef<HTMLInputElement, TimePickerProps>(
  ({ time, onTimeChange, className }, ref) => {
    const formatTime = (time: Date | undefined) => {
      if (!time) return "";
      const hours = time.getHours().toString().padStart(2, '0');
      const minutes = time.getMinutes().toString().padStart(2, '0');
      return `${hours}:${minutes}`;
    };

    const handleTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const timeValue = e.target.value;
      if (!timeValue) return;
      
      const [hours, minutes] = timeValue.split(':').map(Number);
      const newTime = new Date();
      newTime.setHours(hours);
      newTime.setMinutes(minutes);
      newTime.setSeconds(0);
      newTime.setMilliseconds(0);
      
      onTimeChange?.(newTime);
    };

    return (
      <div className={cn("relative", className)}>
        <Input
          ref={ref}
          type="time"
          value={formatTime(time)}
          onChange={handleTimeChange}
          className="w-full"
        />
        <Clock className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400 pointer-events-none" />
      </div>
    );
  }
);

TimePicker.displayName = "TimePicker";

export { TimePicker }
