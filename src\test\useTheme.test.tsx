import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useTheme } from '@/hooks/useTheme'
import { ThemeProvider } from '@/contexts/ThemeProvider'
import React from 'react'

// Mock storage
const mockStorage = {
  get: vi.fn(),
  set: vi.fn()
}

vi.mock('@/lib/storage', () => ({
  storage: mockStorage
}))

vi.mock('@/lib/constants', () => ({
  STORAGE_KEYS: {
    THEME: 'theme'
  }
}))

// Mock window.matchMedia
const mockMatchMedia = vi.fn()

describe('useTheme Hook', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockStorage.get.mockReturnValue(null)
    
    // Mock window.matchMedia
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: mockMatchMedia.mockImplementation(query => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      }))
    })
    
    // Mock document objects
    const mockDocumentElement = {
      classList: { add: vi.fn(), remove: vi.fn() }
    }
    Object.defineProperty(document, 'documentElement', {
      writable: true,
      value: mockDocumentElement
    })
    
    vi.spyOn(document, 'querySelector').mockReturnValue({
      setAttribute: vi.fn()
    } as HTMLMetaElement | null)
  })

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <ThemeProvider>{children}</ThemeProvider>
  )

  it('returns theme and setTheme function', () => {
    const { result } = renderHook(() => useTheme(), { wrapper })
    
    expect(result.current).toHaveProperty('theme')
    expect(result.current).toHaveProperty('setTheme')
    expect(typeof result.current.setTheme).toBe('function')
  })

  it('returns current theme from context', () => {
    mockStorage.get.mockReturnValue('dark')
    
    const { result } = renderHook(() => useTheme(), { wrapper })
    
    expect(result.current.theme).toBe('dark')
  })

  it('setTheme function updates theme correctly', () => {
    const { result } = renderHook(() => useTheme(), { wrapper })
    
    act(() => {
      result.current.setTheme('dark')
    })
    
    expect(result.current.theme).toBe('dark')
    expect(mockStorage.set).toHaveBeenCalledWith('theme', 'dark')
  })

  it('setTheme function is stable across re-renders', () => {
    const { result, rerender } = renderHook(() => useTheme(), { wrapper })
    
    const firstSetTheme = result.current.setTheme
    
    rerender()
    
    const secondSetTheme = result.current.setTheme
    
    expect(firstSetTheme).toBe(secondSetTheme)
  })

  it('throws error when used outside ThemeProvider', () => {
    // Suppress console.error for this test
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
    
    expect(() => {
      renderHook(() => useTheme())
    }).toThrow('useTheme must be used within a ThemeProvider')
    
    consoleSpy.mockRestore()
  })

  it('useCallback dependency works correctly with context changes', () => {
    const { result } = renderHook(() => useTheme(), { wrapper })
    
    const originalSetTheme = result.current.setTheme
    
    // Change theme multiple times
    act(() => {
      result.current.setTheme('dark')
    })
    
    act(() => {
      result.current.setTheme('light')
    })
    
    act(() => {
      result.current.setTheme('system')
    })
    
    // setTheme should remain stable throughout context changes
    expect(result.current.setTheme).toBe(originalSetTheme)
    expect(result.current.theme).toBe('system')
  })

  it('handles all theme values correctly', () => {
    const { result } = renderHook(() => useTheme(), { wrapper })
    
    // Test each theme value
    act(() => {
      result.current.setTheme('light')
    })
    expect(result.current.theme).toBe('light')
    
    act(() => {
      result.current.setTheme('dark')
    })
    expect(result.current.theme).toBe('dark')
    
    act(() => {
      result.current.setTheme('system')
    })
    expect(result.current.theme).toBe('system')
  })

  it('setTheme function calls context.setTheme with correct parameters', () => {
    // Create a custom wrapper with mocked context
    const CustomWrapper = ({ children }: { children: React.ReactNode }) => {
      return (
        <ThemeProvider>
          {children}
        </ThemeProvider>
      )
    }
    
    const { result } = renderHook(() => useTheme(), { wrapper: CustomWrapper })
    
    act(() => {
      result.current.setTheme('dark')
    })
    
    // The actual setTheme should be called
    expect(mockStorage.set).toHaveBeenCalledWith('theme', 'dark')
  })

  it('maintains correct dependencies in useCallback', () => {
    // This test ensures that the useCallback dependency array is correct
    // and doesn't cause unnecessary re-renders or stale closures
    
    let renderCount = 0
    const TestComponent = () => {
      renderCount++
      const { setTheme } = useTheme()
      
      // This callback should be stable
      React.useEffect(() => {
        // Do nothing, just test that setTheme is stable
      }, [setTheme])
      
      return null
    }
    
    const { rerender } = renderHook(() => <TestComponent />, { wrapper })
    
    const initialRenderCount = renderCount
    
    // Multiple re-renders should not cause setTheme to change
    rerender()
    rerender()
    rerender()
    
    // Should not have caused additional renders due to setTheme dependency changes
    expect(renderCount).toBe(initialRenderCount + 3) // One for each rerender
  })
})
