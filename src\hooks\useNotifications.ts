import { useState, useEffect, useCallback } from 'react';
import NotificationService, { 
  Notification, 
  NotificationSettings 
} from '@/services/NotificationService';

interface UseNotificationsReturn {
  // State
  notifications: Notification[];
  unreadCount: number;
  isConnected: boolean;
  settings: NotificationSettings | null;
  
  // Actions
  markAsRead: (notificationId: string) => void;
  markAllAsRead: () => void;
  deleteNotification: (notificationId: string) => void;
  clearAllNotifications: () => void;
  updateSettings: (newSettings: Partial<NotificationSettings>) => void;
  
  // Utilities
  getNotificationsByCategory: (category: string) => Notification[];
  getUnreadNotifications: () => Notification[];
  getImportantNotifications: () => Notification[];
  
  // Service control
  init: () => void;
  destroy: () => void;
}

export const useNotifications = (): UseNotificationsReturn => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isConnected, setIsConnected] = useState(false);
  const [settings, setSettings] = useState<NotificationSettings | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  const notificationService = NotificationService.getInstance();

  // Initialize service and load data
  useEffect(() => {
    if (!isInitialized) {
      init();
      setIsInitialized(true);
    }

    return () => {
      if (isInitialized) {
        destroy();
      }
    };
  }, [isInitialized]);

  // Event handlers
  useEffect(() => {
    const handleConnected = () => {
      setIsConnected(true);
    };

    const handleDisconnected = () => {
      setIsConnected(false);
    };

    const handleNotificationAdded = (notification: Notification) => {
      setNotifications(prev => [notification, ...prev]);
      setUnreadCount(prev => prev + 1);
    };

    const handleNotificationRead = () => {
      loadNotifications();
    };

    const handleAllNotificationsRead = () => {
      setNotifications(prev => prev.map(n => ({ ...n, isRead: true })));
      setUnreadCount(0);
    };

    const handleNotificationDeleted = () => {
      loadNotifications();
    };

    const handleAllNotificationsCleared = () => {
      setNotifications([]);
      setUnreadCount(0);
    };

    const handleSettingsUpdated = (newSettings: NotificationSettings) => {
      setSettings(newSettings);
    };

    // Subscribe to events
    notificationService.on('connected', handleConnected);
    notificationService.on('disconnected', handleDisconnected);
    notificationService.on('notification_added', handleNotificationAdded);
    notificationService.on('notification_read', handleNotificationRead);
    notificationService.on('all_notifications_read', handleAllNotificationsRead);
    notificationService.on('notification_deleted', handleNotificationDeleted);
    notificationService.on('all_notifications_cleared', handleAllNotificationsCleared);
    notificationService.on('settings_updated', handleSettingsUpdated);

    return () => {
      // Cleanup event listeners
      notificationService.off('connected', handleConnected);
      notificationService.off('disconnected', handleDisconnected);
      notificationService.off('notification_added', handleNotificationAdded);
      notificationService.off('notification_read', handleNotificationRead);
      notificationService.off('all_notifications_read', handleAllNotificationsRead);
      notificationService.off('notification_deleted', handleNotificationDeleted);
      notificationService.off('all_notifications_cleared', handleAllNotificationsCleared);
      notificationService.off('settings_updated', handleSettingsUpdated);
    };
  }, [notificationService]);

  const loadNotifications = useCallback(() => {
    const allNotifications = notificationService.getNotifications();
    setNotifications(allNotifications);
    setUnreadCount(notificationService.getUnreadCount());
  }, [notificationService]);

  const loadSettings = useCallback(() => {
    const currentSettings = notificationService.getSettings();
    setSettings(currentSettings);
  }, [notificationService]);

  // Action handlers
  const markAsRead = useCallback((notificationId: string) => {
    notificationService.markAsRead(notificationId);
  }, [notificationService]);

  const markAllAsRead = useCallback(() => {
    notificationService.markAllAsRead();
  }, [notificationService]);

  const deleteNotification = useCallback((notificationId: string) => {
    notificationService.deleteNotification(notificationId);
  }, [notificationService]);

  const clearAllNotifications = useCallback(() => {
    notificationService.clearAllNotifications();
  }, [notificationService]);

  const updateSettings = useCallback((newSettings: Partial<NotificationSettings>) => {
    notificationService.updateSettings(newSettings);
  }, [notificationService]);

  // Utility functions
  const getNotificationsByCategory = useCallback((category: string) => {
    return notificationService.getNotificationsByCategory(category);
  }, [notificationService]);

  const getUnreadNotifications = useCallback(() => {
    return notifications.filter(n => !n.isRead);
  }, [notifications]);

  const getImportantNotifications = useCallback(() => {
    return notifications.filter(n => n.isImportant);
  }, [notifications]);

  // Service control
  const init = useCallback(() => {
    notificationService.init();
    loadNotifications();
    loadSettings();
  }, [notificationService, loadNotifications, loadSettings]);

  const destroy = useCallback(() => {
    notificationService.destroy();
  }, [notificationService]);

  return {
    // State
    notifications,
    unreadCount,
    isConnected,
    settings,
    
    // Actions
    markAsRead,
    markAllAsRead,
    deleteNotification,
    clearAllNotifications,
    updateSettings,
    
    // Utilities
    getNotificationsByCategory,
    getUnreadNotifications,
    getImportantNotifications,
    
    // Service control
    init,
    destroy
  };
};

// Hook for creating notifications (useful for testing or manual notifications)
export const useCreateNotification = () => {
  const notificationService = NotificationService.getInstance();

  const createNotification = useCallback((notification: Omit<Notification, 'id' | 'timestamp'>) => {
    const fullNotification: Notification = {
      ...notification,
      id: `notif-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date()
    };
    
    notificationService.addNotification(fullNotification);
    return fullNotification.id;
  }, [notificationService]);

  const createSocialNotification = useCallback((
    type: 'like' | 'comment' | 'share' | 'friend_request' | 'tag',
    userName: string,
    userAvatar?: string,
    actionUrl?: string
  ) => {
    const messages = {
      like: `${userName} liked your post`,
      comment: `${userName} commented on your post`,
      share: `${userName} shared your post`,
      friend_request: `${userName} sent you a friend request`,
      tag: `${userName} tagged you in a post`
    };

    const titles = {
      like: 'New Like',
      comment: 'New Comment',
      share: 'Post Shared',
      friend_request: 'Friend Request',
      tag: 'You were tagged'
    };

    return createNotification({
      type,
      title: titles[type],
      message: messages[type],
      isRead: false,
      isImportant: type === 'friend_request',
      priority: type === 'friend_request' ? 'high' : 'medium',
      category: 'social',
      user: {
        id: `user-${userName.toLowerCase().replace(' ', '-')}`,
        name: userName,
        avatar: userAvatar
      },
      actionUrl,
      soundEnabled: true,
      vibrationEnabled: true
    });
  }, [createNotification]);

  const createSystemNotification = useCallback((
    title: string,
    message: string,
    priority: 'low' | 'medium' | 'high' | 'urgent' = 'medium',
    actionUrl?: string
  ) => {
    return createNotification({
      type: 'system',
      title,
      message,
      isRead: false,
      isImportant: priority === 'urgent',
      priority,
      category: 'system',
      actionUrl,
      soundEnabled: priority !== 'low',
      vibrationEnabled: priority === 'urgent'
    });
  }, [createNotification]);

  const createSecurityNotification = useCallback((
    title: string,
    message: string,
    actionUrl?: string
  ) => {
    return createNotification({
      type: 'system',
      title,
      message,
      isRead: false,
      isImportant: true,
      priority: 'urgent',
      category: 'security',
      actionUrl,
      soundEnabled: true,
      vibrationEnabled: true
    });
  }, [createNotification]);

  return {
    createNotification,
    createSocialNotification,
    createSystemNotification,
    createSecurityNotification
  };
};
