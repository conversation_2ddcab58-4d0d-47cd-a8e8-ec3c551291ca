import { EventEmitter } from '@/utils/EventEmitter';

export interface CallParticipant {
  id: string;
  name: string;
  avatar?: string;
  isVideoEnabled: boolean;
  isAudioEnabled: boolean;
  isScreenSharing: boolean;
  stream?: MediaStream;
  peerConnection?: RTCPeerConnection;
}

export interface CallSession {
  id: string;
  type: 'video' | 'audio' | 'screen';
  participants: CallParticipant[];
  startTime: Date;
  isRecording: boolean;
  recordingUrl?: string;
  chatMessages: ChatMessage[];
}

export interface ChatMessage {
  id: string;
  senderId: string;
  senderName: string;
  message: string;
  timestamp: Date;
}

export interface CallInvitation {
  id: string;
  callId: string;
  fromUserId: string;
  fromUserName: string;
  toUserId: string;
  type: 'video' | 'audio';
  timestamp: Date;
}

class VideoCallService extends EventEmitter {
  private static instance: VideoCallService;
  private currentCall: CallSession | null = null;
  private localStream: MediaStream | null = null;
  private screenStream: MediaStream | null = null;
  private mediaRecorder: MediaRecorder | null = null;
  private recordedChunks: Blob[] = [];
  private ws: WebSocket | null = null;
  private currentUserId: string = 'current-user';

  // WebRTC configuration
  private rtcConfig: RTCConfiguration = {
    iceServers: [
      { urls: 'stun:stun.l.google.com:19302' },
      { urls: 'stun:stun1.l.google.com:19302' },
    ],
  };

  static getInstance(): VideoCallService {
    if (!VideoCallService.instance) {
      VideoCallService.instance = new VideoCallService();
    }
    return VideoCallService.instance;
  }

  constructor() {
    super();
    this.initializeWebSocket();
  }

  private initializeWebSocket() {
    try {
      // In production, this would be your signaling server
      this.ws = new WebSocket('ws://localhost:8080/ws/video-calls');
      
      this.ws.onopen = () => {
        console.log('Video call WebSocket connected');
        this.emit('connected');
      };

      this.ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        this.handleSignalingMessage(data);
      };

      this.ws.onclose = () => {
        console.log('Video call WebSocket disconnected');
        this.emit('disconnected');
        // Attempt to reconnect after 3 seconds
        setTimeout(() => this.initializeWebSocket(), 3000);
      };

      this.ws.onerror = (error) => {
        console.error('Video call WebSocket error:', error);
      };
    } catch (error) {
      console.warn('WebSocket not available, using mock implementation');
      this.initializeMockSignaling();
    }
  }

  private initializeMockSignaling() {
    // Mock signaling for demo purposes
    setTimeout(() => {
      this.emit('connected');
    }, 100);
  }

  private handleSignalingMessage(data: { type: string; [key: string]: unknown }) {
    switch (data.type) {
      case 'call-invitation':
        this.emit('call-invitation', data.invitation);
        break;
      case 'call-accepted':
        this.handleCallAccepted(data);
        break;
      case 'call-rejected':
        this.emit('call-rejected', data);
        break;
      case 'call-ended':
        this.handleCallEnded();
        break;
      case 'offer':
        this.handleOffer(data);
        break;
      case 'answer':
        this.handleAnswer(data);
        break;
      case 'ice-candidate':
        this.handleIceCandidate(data);
        break;
      case 'participant-joined':
        this.handleParticipantJoined(data);
        break;
      case 'participant-left':
        this.handleParticipantLeft(data);
        break;
      case 'media-toggle':
        this.handleMediaToggle(data);
        break;
      case 'chat-message':
        this.handleChatMessage(data);
        break;
    }
  }

  // Initialize local media
  async initializeMedia(video: boolean = true, audio: boolean = true): Promise<MediaStream> {
    try {
      const constraints: MediaStreamConstraints = {
        video: video ? {
          width: { ideal: 1280 },
          height: { ideal: 720 },
          frameRate: { ideal: 30 }
        } : false,
        audio: audio ? {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        } : false
      };

      this.localStream = await navigator.mediaDevices.getUserMedia(constraints);
      this.emit('local-stream', this.localStream);
      return this.localStream;
    } catch (error) {
      console.error('Error accessing media devices:', error);
      throw new Error('Failed to access camera/microphone');
    }
  }

  // Start screen sharing
  async startScreenShare(): Promise<MediaStream> {
    try {
      this.screenStream = await navigator.mediaDevices.getDisplayMedia({
        video: {
          width: { ideal: 1920 },
          height: { ideal: 1080 },
          frameRate: { ideal: 30 }
        },
        audio: true
      });

      // Handle screen share end
      this.screenStream.getVideoTracks()[0].onended = () => {
        this.stopScreenShare();
      };

      this.emit('screen-share-started', this.screenStream);
      this.broadcastMediaToggle('screen', true);
      return this.screenStream;
    } catch (error) {
      console.error('Error starting screen share:', error);
      throw new Error('Failed to start screen sharing');
    }
  }

  // Stop screen sharing
  stopScreenShare() {
    if (this.screenStream) {
      this.screenStream.getTracks().forEach(track => track.stop());
      this.screenStream = null;
      this.emit('screen-share-stopped');
      this.broadcastMediaToggle('screen', false);
    }
  }

  // Start a new call
  async startCall(participantIds: string[], type: 'video' | 'audio' = 'video'): Promise<string> {
    const callId = `call-${Date.now()}`;
    
    // Initialize local media
    await this.initializeMedia(type === 'video', true);

    // Create call session
    this.currentCall = {
      id: callId,
      type,
      participants: [{
        id: this.currentUserId,
        name: 'You',
        isVideoEnabled: type === 'video',
        isAudioEnabled: true,
        isScreenSharing: false,
        stream: this.localStream || undefined
      }],
      startTime: new Date(),
      isRecording: false,
      chatMessages: []
    };

    // Send invitations to participants
    for (const participantId of participantIds) {
      this.sendCallInvitation(callId, participantId, type);
    }

    this.emit('call-started', this.currentCall);
    return callId;
  }

  // Send call invitation
  private sendCallInvitation(callId: string, toUserId: string, type: 'video' | 'audio') {
    const invitation: CallInvitation = {
      id: `inv-${Date.now()}`,
      callId,
      fromUserId: this.currentUserId,
      fromUserName: 'You',
      toUserId,
      type,
      timestamp: new Date()
    };

    this.sendSignalingMessage({
      type: 'call-invitation',
      invitation,
      toUserId
    });
  }

  // Accept incoming call
  async acceptCall(invitation: CallInvitation): Promise<void> {
    try {
      // Initialize local media
      await this.initializeMedia(invitation.type === 'video', true);

      // Create call session
      this.currentCall = {
        id: invitation.callId,
        type: invitation.type,
        participants: [{
          id: this.currentUserId,
          name: 'You',
          isVideoEnabled: invitation.type === 'video',
          isAudioEnabled: true,
          isScreenSharing: false,
          stream: this.localStream || undefined
        }],
        startTime: new Date(),
        isRecording: false,
        chatMessages: []
      };

      // Send acceptance
      this.sendSignalingMessage({
        type: 'call-accepted',
        callId: invitation.callId,
        fromUserId: invitation.fromUserId
      });

      this.emit('call-accepted', this.currentCall);
    } catch (error) {
      console.error('Error accepting call:', error);
      this.rejectCall(invitation);
    }
  }

  // Reject incoming call
  rejectCall(invitation: CallInvitation) {
    this.sendSignalingMessage({
      type: 'call-rejected',
      callId: invitation.callId,
      fromUserId: invitation.fromUserId
    });
  }

  // End current call
  endCall() {
    if (this.currentCall) {
      // Stop all media streams
      if (this.localStream) {
        this.localStream.getTracks().forEach(track => track.stop());
        this.localStream = null;
      }

      if (this.screenStream) {
        this.screenStream.getTracks().forEach(track => track.stop());
        this.screenStream = null;
      }

      // Stop recording if active
      if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
        this.stopRecording();
      }

      // Close peer connections
      this.currentCall.participants.forEach(participant => {
        if (participant.peerConnection) {
          participant.peerConnection.close();
        }
      });

      // Notify other participants
      this.sendSignalingMessage({
        type: 'call-ended',
        callId: this.currentCall.id
      });

      const endedCall = this.currentCall;
      this.currentCall = null;
      this.emit('call-ended', endedCall);
    }
  }

  // Toggle video
  toggleVideo(): boolean {
    if (this.localStream) {
      const videoTrack = this.localStream.getVideoTracks()[0];
      if (videoTrack) {
        videoTrack.enabled = !videoTrack.enabled;
        this.broadcastMediaToggle('video', videoTrack.enabled);
        this.emit('video-toggled', videoTrack.enabled);
        return videoTrack.enabled;
      }
    }
    return false;
  }

  // Toggle audio
  toggleAudio(): boolean {
    if (this.localStream) {
      const audioTrack = this.localStream.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = !audioTrack.enabled;
        this.broadcastMediaToggle('audio', audioTrack.enabled);
        this.emit('audio-toggled', audioTrack.enabled);
        return audioTrack.enabled;
      }
    }
    return false;
  }

  // Start call recording
  async startRecording(): Promise<void> {
    if (!this.currentCall || this.currentCall.isRecording) return;

    try {
      // Create a canvas to combine all video streams
      const canvas = document.createElement('canvas');
      canvas.width = 1920;
      canvas.height = 1080;
      const ctx = canvas.getContext('2d');

      // Get canvas stream
      const canvasStream = canvas.captureStream(30);
      
      // Add audio from local stream
      if (this.localStream) {
        const audioTracks = this.localStream.getAudioTracks();
        audioTracks.forEach(track => canvasStream.addTrack(track));
      }

      this.mediaRecorder = new MediaRecorder(canvasStream, {
        mimeType: 'video/webm;codecs=vp9'
      });

      this.recordedChunks = [];

      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.recordedChunks.push(event.data);
        }
      };

      this.mediaRecorder.onstop = () => {
        const blob = new Blob(this.recordedChunks, { type: 'video/webm' });
        const url = URL.createObjectURL(blob);
        
        if (this.currentCall) {
          this.currentCall.recordingUrl = url;
          this.currentCall.isRecording = false;
        }
        
        this.emit('recording-stopped', url);
      };

      this.mediaRecorder.start(1000); // Record in 1-second chunks
      this.currentCall.isRecording = true;
      this.emit('recording-started');
    } catch (error) {
      console.error('Error starting recording:', error);
      throw new Error('Failed to start recording');
    }
  }

  // Stop call recording
  stopRecording(): void {
    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
      this.mediaRecorder.stop();
    }
  }

  // Send chat message during call
  sendChatMessage(message: string): void {
    if (!this.currentCall) return;

    const chatMessage: ChatMessage = {
      id: `msg-${Date.now()}`,
      senderId: this.currentUserId,
      senderName: 'You',
      message,
      timestamp: new Date()
    };

    this.currentCall.chatMessages.push(chatMessage);

    this.sendSignalingMessage({
      type: 'chat-message',
      callId: this.currentCall.id,
      message: chatMessage
    });

    this.emit('chat-message', chatMessage);
  }

  // Private helper methods
  private sendSignalingMessage(message: Record<string, unknown>) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      // Mock implementation for demo
      console.log('Mock signaling message:', message);
    }
  }

  private broadcastMediaToggle(type: 'video' | 'audio' | 'screen', enabled: boolean) {
    if (this.currentCall) {
      this.sendSignalingMessage({
        type: 'media-toggle',
        callId: this.currentCall.id,
        userId: this.currentUserId,
        mediaType: type,
        enabled
      });
    }
  }

  private handleCallAccepted(data: Record<string, unknown>) {
    this.emit('call-accepted', data);
  }

  private handleCallEnded() {
    if (this.currentCall) {
      this.endCall();
    }
  }

  private handleOffer(data: Record<string, unknown>) {
    // Handle WebRTC offer
    this.emit('offer-received', data);
  }

  private handleAnswer(data: Record<string, unknown>) {
    // Handle WebRTC answer
    this.emit('answer-received', data);
  }

  private handleIceCandidate(data: Record<string, unknown>) {
    // Handle ICE candidate
    this.emit('ice-candidate-received', data);
  }

  private handleParticipantJoined(data: Record<string, unknown>) {
    if (this.currentCall) {
      // Add new participant
      this.emit('participant-joined', data);
    }
  }

  private handleParticipantLeft(data: { userId: string }) {
    if (this.currentCall) {
      // Remove participant
      this.currentCall.participants = this.currentCall.participants.filter(
        p => p.id !== data.userId
      );
      this.emit('participant-left', data);
    }
  }

  private handleMediaToggle(data: { userId: string; mediaType: string; enabled: boolean }) {
    if (this.currentCall) {
      const participant = this.currentCall.participants.find(p => p.id === data.userId);
      if (participant) {
        switch (data.mediaType) {
          case 'video':
            participant.isVideoEnabled = data.enabled;
            break;
          case 'audio':
            participant.isAudioEnabled = data.enabled;
            break;
          case 'screen':
            participant.isScreenSharing = data.enabled;
            break;
        }
        this.emit('media-toggled', data);
      }
    }
  }

  private handleChatMessage(data: { message: ChatMessage }) {
    if (this.currentCall) {
      this.currentCall.chatMessages.push(data.message);
      this.emit('chat-message', data.message);
    }
  }

  // Getters
  getCurrentCall(): CallSession | null {
    return this.currentCall;
  }

  getLocalStream(): MediaStream | null {
    return this.localStream;
  }

  getScreenStream(): MediaStream | null {
    return this.screenStream;
  }

  isInCall(): boolean {
    return this.currentCall !== null;
  }
}

export default VideoCallService;
