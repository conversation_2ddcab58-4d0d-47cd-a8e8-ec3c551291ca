const CACHE_NAME = 'facebook-clone-v2.0.1';
const API_CACHE_NAME = 'api-cache-v1';
const IMAGE_CACHE_NAME = 'image-cache-v1';

// Files to cache for offline support
const STATIC_CACHE_URLS = [
  '/',
  '/manifest.json',
  '/static/js/bundle.js',
  '/static/css/main.css',
  '/favicon.ico',
  '/placeholder.svg'
];

// API endpoints to cache
const API_CACHE_PATTERNS = [
  '/api/posts',
  '/api/users',
  '/api/notifications',
  '/api/stories'
];

// Image cache patterns
const IMAGE_CACHE_PATTERNS = [
  /\.(jpg|jpeg|png|gif|webp|svg)$/i,
  /\/images\//,
  /\/avatars\//,
  /dicebear\.com/,
  /pexels\.com/
];

// Install event - cache static files
self.addEventListener('install', (event) => {
  console.log('Service Worker installing...');
  
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('Caching static files');
        return cache.addAll(STATIC_CACHE_URLS);
      })
      .then(() => {
        // Skip waiting to activate immediately
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('Cache installation failed:', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker activating...');
  
  event.waitUntil(
    Promise.all([
      // Clean up old caches
      caches.keys().then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== CACHE_NAME && 
                cacheName !== API_CACHE_NAME && 
                cacheName !== IMAGE_CACHE_NAME) {
              console.log('Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      }),
      // Take control of all clients
      self.clients.claim()
    ])
  );
});

// Fetch event - handle network requests
self.addEventListener('fetch', (event) => {
  const request = event.request;
  const url = new URL(request.url);

  // Handle different types of requests
  if (request.method === 'GET') {
    if (isStaticAsset(url)) {
      event.respondWith(handleStaticAsset(request));
    } else if (isApiRequest(url)) {
      event.respondWith(handleApiRequest(request));
    } else if (isImageRequest(url)) {
      event.respondWith(handleImageRequest(request));
    } else {
      event.respondWith(handleGenericRequest(request));
    }
  } else if (request.method === 'POST' || request.method === 'PUT') {
    event.respondWith(handleMutatingRequest(request));
  }
});

// Handle static assets with cache-first strategy
async function handleStaticAsset(request) {
  try {
    const cache = await caches.open(CACHE_NAME);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      // Return cached version immediately
      fetchAndUpdateCache(request, cache);
      return cachedResponse;
    }
    
    // If not in cache, fetch and cache
    const response = await fetch(request);
    if (response.status === 200) {
      cache.put(request, response.clone());
    }
    return response;
  } catch (error) {
    console.error('Static asset fetch failed:', error);
    return new Response('Offline - content not available', {
      status: 503,
      statusText: 'Service Unavailable'
    });
  }
}

// Handle API requests with network-first strategy
async function handleApiRequest(request) {
  try {
    const cache = await caches.open(API_CACHE_NAME);
    
    // Try network first
    try {
      const response = await fetch(request);
      
      if (response.status === 200) {
        // Cache successful responses
        cache.put(request, response.clone());
      }
      
      return response;
    } catch (networkError) {
      // Network failed, try cache
      const cachedResponse = await cache.match(request);
      
      if (cachedResponse) {
        // Add offline indicator header
        const offlineResponse = cachedResponse.clone();
        offlineResponse.headers.append('X-Served-From', 'cache');
        return offlineResponse;
      }
      
      // Return offline response
      return new Response(JSON.stringify({
        error: 'Offline - no cached data available',
        offline: true
      }), {
        status: 503,
        statusText: 'Service Unavailable',
        headers: {
          'Content-Type': 'application/json',
          'X-Served-From': 'offline'
        }
      });
    }
  } catch (error) {
    console.error('API request failed:', error);
    return new Response(JSON.stringify({ error: 'Request failed' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// Handle image requests with cache-first strategy
async function handleImageRequest(request) {
  try {
    const cache = await caches.open(IMAGE_CACHE_NAME);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    const response = await fetch(request);
    
    if (response.status === 200) {
      // Cache images for future use
      cache.put(request, response.clone());
    }
    
    return response;
  } catch (error) {
    console.error('Image fetch failed:', error);
    
    // Return placeholder image for failed requests
    return fetch('/placeholder.svg').catch(() => {
      return new Response('', {
        status: 404,
        statusText: 'Image not found'
      });
    });
  }
}

// Handle generic requests
async function handleGenericRequest(request) {
  try {
    return await fetch(request);
  } catch (error) {
    // For navigation requests, return cached index.html
    if (request.mode === 'navigate') {
      const cache = await caches.open(CACHE_NAME);
      const cachedIndex = await cache.match('/');
      return cachedIndex || new Response('Offline', { status: 503 });
    }
    
    return new Response('Offline', { status: 503 });
  }
}

// Handle mutating requests (POST, PUT, DELETE)
async function handleMutatingRequest(request) {
  try {
    const response = await fetch(request);
    
    // If successful, invalidate related cache entries
    if (response.status < 400) {
      await invalidateRelatedCache(request);
    }
    
    return response;
  } catch (error) {
    // Store failed requests for retry when online
    await storeFailedRequest(request);
    
    return new Response(JSON.stringify({
      error: 'Request queued for retry when online',
      queued: true
    }), {
      status: 202,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// Background sync for failed requests
self.addEventListener('sync', (event) => {
  if (event.tag === 'background-sync') {
    event.waitUntil(retryFailedRequests());
  }
});

// Message handling for cache management
self.addEventListener('message', (event) => {
  const { type, payload } = event.data;
  
  switch (type) {
    case 'CACHE_UPDATE':
      event.waitUntil(updateCache(payload));
      break;
    case 'CACHE_CLEAR':
      event.waitUntil(clearCache(payload.cacheName));
      break;
    case 'PREFETCH_RESOURCES':
      event.waitUntil(prefetchResources(payload.urls));
      break;
    case 'GET_CACHE_SIZE':
      event.waitUntil(getCacheSize().then(size => {
        event.ports[0].postMessage({ type: 'CACHE_SIZE', payload: size });
      }));
      break;
  }
});

// Utility functions
function isStaticAsset(url) {
  return STATIC_CACHE_URLS.some(pattern => url.pathname.includes(pattern)) ||
         url.pathname.endsWith('.js') ||
         url.pathname.endsWith('.css') ||
         url.pathname.endsWith('.html');
}

function isApiRequest(url) {
  return API_CACHE_PATTERNS.some(pattern => url.pathname.includes(pattern)) ||
         url.pathname.startsWith('/api/');
}

function isImageRequest(url) {
  return IMAGE_CACHE_PATTERNS.some(pattern => {
    if (pattern instanceof RegExp) {
      return pattern.test(url.pathname) || pattern.test(url.hostname);
    }
    return url.pathname.includes(pattern);
  });
}

// Fetch and update cache in background
async function fetchAndUpdateCache(request, cache) {
  try {
    const response = await fetch(request);
    if (response.status === 200) {
      await cache.put(request, response);
    }
  } catch (error) {
    // Ignore background update errors
    console.log('Background cache update failed:', error);
  }
}

// Store failed requests for retry
async function storeFailedRequest(request) {
  try {
    const requestData = {
      url: request.url,
      method: request.method,
      headers: Object.fromEntries(request.headers.entries()),
      body: request.method !== 'GET' ? await request.text() : null,
      timestamp: Date.now()
    };
    
    const failedRequests = await getFailedRequests();
    failedRequests.push(requestData);
    
    await self.registration.sync.register('background-sync');
    
    // Store in IndexedDB (simplified storage in localStorage for this example)
    const cache = await caches.open('failed-requests');
    await cache.put('/failed-requests', new Response(JSON.stringify(failedRequests)));
  } catch (error) {
    console.error('Failed to store request for retry:', error);
  }
}

// Get failed requests from storage
async function getFailedRequests() {
  try {
    const cache = await caches.open('failed-requests');
    const response = await cache.match('/failed-requests');
    if (response) {
      return await response.json();
    }
    return [];
  } catch (error) {
    return [];
  }
}

// Retry failed requests
async function retryFailedRequests() {
  try {
    const failedRequests = await getFailedRequests();
    const retryPromises = [];
    const remainingRequests = [];
    
    for (const requestData of failedRequests) {
      try {
        const request = new Request(requestData.url, {
          method: requestData.method,
          headers: requestData.headers,
          body: requestData.body
        });
        
        const response = await fetch(request);
        
        if (response.status >= 400) {
          // Keep for retry if still failing
          remainingRequests.push(requestData);
        }
      } catch (error) {
        // Keep for retry
        remainingRequests.push(requestData);
      }
    }
    
    // Update stored failed requests
    const cache = await caches.open('failed-requests');
    await cache.put('/failed-requests', new Response(JSON.stringify(remainingRequests)));
    
    // Notify clients about sync completion
    const clients = await self.clients.matchAll();
    clients.forEach(client => {
      client.postMessage({
        type: 'SYNC_COMPLETE',
        payload: {
          processed: failedRequests.length - remainingRequests.length,
          remaining: remainingRequests.length
        }
      });
    });
  } catch (error) {
    console.error('Failed to retry requests:', error);
  }
}

// Invalidate related cache entries
async function invalidateRelatedCache(request) {
  try {
    const url = new URL(request.url);
    const cache = await caches.open(API_CACHE_NAME);
    
    // Invalidate related API cache entries
    if (url.pathname.includes('/posts')) {
      await cache.delete('/api/posts');
    }
    if (url.pathname.includes('/users')) {
      await cache.delete('/api/users');
    }
  } catch (error) {
    console.error('Failed to invalidate cache:', error);
  }
}

// Update cache with new data
async function updateCache(payload) {
  try {
    const { cacheName, url, data } = payload;
    const cache = await caches.open(cacheName || API_CACHE_NAME);
    
    const response = new Response(JSON.stringify(data), {
      headers: { 'Content-Type': 'application/json' }
    });
    
    await cache.put(url, response);
  } catch (error) {
    console.error('Failed to update cache:', error);
  }
}

// Clear specific cache
async function clearCache(cacheName) {
  try {
    await caches.delete(cacheName);
  } catch (error) {
    console.error('Failed to clear cache:', error);
  }
}

// Prefetch resources
async function prefetchResources(urls) {
  try {
    const cache = await caches.open(CACHE_NAME);
    
    for (const url of urls) {
      try {
        const response = await fetch(url);
        if (response.status === 200) {
          await cache.put(url, response);
        }
      } catch (error) {
        console.log('Failed to prefetch:', url, error);
      }
    }
  } catch (error) {
    console.error('Failed to prefetch resources:', error);
  }
}

// Get cache size information
async function getCacheSize() {
  try {
    const cacheNames = await caches.keys();
    const sizes = {};
    
    for (const cacheName of cacheNames) {
      const cache = await caches.open(cacheName);
      const keys = await cache.keys();
      sizes[cacheName] = keys.length;
    }
    
    return sizes;
  } catch (error) {
    console.error('Failed to get cache size:', error);
    return {};
  }
}

// Performance monitoring
self.addEventListener('fetch', (event) => {
  // Track fetch performance
  const startTime = performance.now();
  
  event.respondWith(
    (async () => {
      try {
        const response = await fetch(event.request);
        const endTime = performance.now();
        
        // Log slow requests
        if (endTime - startTime > 5000) {
          console.warn('Slow request detected:', event.request.url, `${endTime - startTime}ms`);
        }
        
        return response;
      } catch (error) {
        console.error('Fetch failed:', event.request.url, error);
        throw error;
      }
    })()
  );
});

// Cleanup old cached entries periodically
setInterval(async () => {
  try {
    const cache = await caches.open(IMAGE_CACHE_NAME);
    const keys = await cache.keys();
    
    // Remove oldest entries if cache is too large
    if (keys.length > 1000) {
      const entriesToRemove = keys.slice(0, keys.length - 500);
      await Promise.all(entriesToRemove.map(key => cache.delete(key)));
    }
  } catch (error) {
    console.error('Cache cleanup failed:', error);
  }
}, 60000 * 30); // Run every 30 minutes
