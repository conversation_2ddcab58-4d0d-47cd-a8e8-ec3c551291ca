/**
 * Browser-compatible EventEmitter implementation
 * Replaces Node.js EventEmitter for browser environments
 */

type EventHandler = (...args: unknown[]) => void;

export class EventEmitter {
  private events: Map<string, EventHandler[]> = new Map();
  private maxListeners: number = 10;

  /**
   * Add an event listener
   */
  on(event: string, listener: EventHandler): this {
    if (!this.events.has(event)) {
      this.events.set(event, []);
    }
    
    const listeners = this.events.get(event)!;
    listeners.push(listener);
    
    // Warn if too many listeners
    if (listeners.length > this.maxListeners) {
      console.warn(`Warning: Possible EventEmitter memory leak detected. ${listeners.length} listeners added for event '${event}'.`);
    }
    
    return this;
  }

  /**
   * Add a one-time event listener
   */
  once(event: string, listener: EventHandler): this {
    const onceWrapper = (...args: unknown[]) => {
      this.off(event, onceWrapper);
      listener(...args);
    };
    
    return this.on(event, onceWrapper);
  }

  /**
   * Remove an event listener
   */
  off(event: string, listener: EventH<PERSON>ler): this {
    const listeners = this.events.get(event);
    if (!listeners) return this;
    
    const index = listeners.indexOf(listener);
    if (index > -1) {
      listeners.splice(index, 1);
      
      // Clean up empty arrays
      if (listeners.length === 0) {
        this.events.delete(event);
      }
    }
    
    return this;
  }

  /**
   * Emit an event
   */
  emit(event: string, ...args: unknown[]): boolean {
    const listeners = this.events.get(event);
    if (!listeners || listeners.length === 0) {
      return false;
    }
    
    // Create a copy to avoid issues if listeners are removed during emission
    const listenersCopy = [...listeners];
    
    listenersCopy.forEach(listener => {
      try {
        listener(...args);
      } catch (error) {
        console.error(`Error in event listener for '${event}':`, error);
      }
    });
    
    return true;
  }

  /**
   * Remove all listeners for an event, or all listeners for all events
   */
  removeAllListeners(event?: string): this {
    if (event) {
      this.events.delete(event);
    } else {
      this.events.clear();
    }
    
    return this;
  }

  /**
   * Get all listeners for an event
   */
  listeners(event: string): EventHandler[] {
    return [...(this.events.get(event) || [])];
  }

  /**
   * Get the number of listeners for an event
   */
  listenerCount(event: string): number {
    return this.events.get(event)?.length || 0;
  }

  /**
   * Get all event names
   */
  eventNames(): string[] {
    return Array.from(this.events.keys());
  }

  /**
   * Set maximum number of listeners before warning
   */
  setMaxListeners(n: number): this {
    this.maxListeners = n;
    return this;
  }

  /**
   * Get maximum number of listeners
   */
  getMaxListeners(): number {
    return this.maxListeners;
  }
}

export default EventEmitter;
