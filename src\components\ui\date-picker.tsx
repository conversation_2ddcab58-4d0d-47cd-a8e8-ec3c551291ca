import * as React from "react"
import { Calendar } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"

export interface DatePickerProps {
  date?: Date;
  onDateChange?: (date: Date | undefined) => void;
  disabled?: (date: Date) => boolean;
  className?: string;
}

const DatePicker = React.forwardRef<HTMLInputElement, DatePickerProps>(
  ({ date, onDateChange, disabled, className }, ref) => {
    const formatDate = (date: Date | undefined) => {
      if (!date) return "";
      return date.toISOString().split('T')[0];
    };

    const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const newDate = e.target.value ? new Date(e.target.value) : undefined;
      if (newDate && disabled && disabled(newDate)) return;
      onDateChange?.(newDate);
    };

    return (
      <div className={cn("relative", className)}>
        <Input
          ref={ref}
          type="date"
          value={formatDate(date)}
          onChange={handleDateChange}
          className="w-full"
        />
        <Calendar className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400 pointer-events-none" />
      </div>
    );
  }
);

DatePicker.displayName = "DatePicker";

export { DatePicker }
