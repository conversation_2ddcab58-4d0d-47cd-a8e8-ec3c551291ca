// Bundle optimization utilities and performance monitoring

interface BundleMetrics {
  totalSize: number;
  gzippedSize: number;
  chunks: ChunkInfo[];
  loadTime: number;
  renderTime: number;
  interactiveTime: number;
}

interface ChunkInfo {
  name: string;
  size: number;
  gzippedSize: number;
  modules: string[];
  isAsync: boolean;
  priority: 'high' | 'medium' | 'low';
}

interface PerformanceMetrics {
  fcp: number; // First Contentful Paint
  lcp: number; // Largest Contentful Paint
  fid: number; // First Input Delay
  cls: number; // Cumulative Layout Shift
  ttfb: number; // Time to First Byte
}

// Performance monitoring class
export class PerformanceMonitor {
  private metrics: PerformanceMetrics = {
    fcp: 0,
    lcp: 0,
    fid: 0,
    cls: 0,
    ttfb: 0
  };

  private observers: PerformanceObserver[] = [];

  constructor() {
    this.initializeObservers();
  }

  private initializeObservers() {
    // First Contentful Paint
    if ('PerformanceObserver' in window) {
      try {
        const fcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');
          if (fcpEntry) {
            this.metrics.fcp = fcpEntry.startTime;
          }
        });
        fcpObserver.observe({ entryTypes: ['paint'] });
        this.observers.push(fcpObserver);
      } catch (e) {
        console.warn('FCP observer not supported');
      }

      // Largest Contentful Paint
      try {
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          this.metrics.lcp = lastEntry.startTime;
        });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
        this.observers.push(lcpObserver);
      } catch (e) {
        console.warn('LCP observer not supported');
      }

      // First Input Delay
      try {
        const fidObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            this.metrics.fid = entry.processingStart - entry.startTime;
          });
        });
        fidObserver.observe({ entryTypes: ['first-input'] });
        this.observers.push(fidObserver);
      } catch (e) {
        console.warn('FID observer not supported');
      }

      // Cumulative Layout Shift
      try {
        const clsObserver = new PerformanceObserver((list) => {
          let clsValue = 0;
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            if (!entry.hadRecentInput) {
              clsValue += entry.value;
            }
          });
          this.metrics.cls = clsValue;
        });
        clsObserver.observe({ entryTypes: ['layout-shift'] });
        this.observers.push(clsObserver);
      } catch (e) {
        console.warn('CLS observer not supported');
      }
    }

    // Time to First Byte
    if ('performance' in window && 'timing' in performance) {
      window.addEventListener('load', () => {
        const timing = performance.timing;
        this.metrics.ttfb = timing.responseStart - timing.navigationStart;
      });
    }
  }

  getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  getScore(): number {
    // Calculate a performance score based on Core Web Vitals
    let score = 100;
    
    // LCP scoring (good: <2.5s, needs improvement: 2.5-4s, poor: >4s)
    if (this.metrics.lcp > 4000) score -= 30;
    else if (this.metrics.lcp > 2500) score -= 15;
    
    // FID scoring (good: <100ms, needs improvement: 100-300ms, poor: >300ms)
    if (this.metrics.fid > 300) score -= 25;
    else if (this.metrics.fid > 100) score -= 10;
    
    // CLS scoring (good: <0.1, needs improvement: 0.1-0.25, poor: >0.25)
    if (this.metrics.cls > 0.25) score -= 25;
    else if (this.metrics.cls > 0.1) score -= 10;
    
    // FCP scoring (good: <1.8s, needs improvement: 1.8-3s, poor: >3s)
    if (this.metrics.fcp > 3000) score -= 20;
    else if (this.metrics.fcp > 1800) score -= 10;
    
    return Math.max(0, score);
  }

  disconnect() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }
}

// Bundle size analyzer
export class BundleAnalyzer {
  private static instance: BundleAnalyzer;
  private chunks: Map<string, ChunkInfo> = new Map();

  static getInstance(): BundleAnalyzer {
    if (!BundleAnalyzer.instance) {
      BundleAnalyzer.instance = new BundleAnalyzer();
    }
    return BundleAnalyzer.instance;
  }

  registerChunk(name: string, info: Omit<ChunkInfo, 'name'>) {
    this.chunks.set(name, { name, ...info });
  }

  getChunkInfo(name: string): ChunkInfo | undefined {
    return this.chunks.get(name);
  }

  getAllChunks(): ChunkInfo[] {
    return Array.from(this.chunks.values());
  }

  getTotalSize(): number {
    return Array.from(this.chunks.values()).reduce((total, chunk) => total + chunk.size, 0);
  }

  getTotalGzippedSize(): number {
    return Array.from(this.chunks.values()).reduce((total, chunk) => total + chunk.gzippedSize, 0);
  }

  getUnusedChunks(): ChunkInfo[] {
    // This would need to be implemented with actual usage tracking
    return this.getAllChunks().filter(chunk => chunk.priority === 'low');
  }

  generateReport(): BundleMetrics {
    const chunks = this.getAllChunks();
    return {
      totalSize: this.getTotalSize(),
      gzippedSize: this.getTotalGzippedSize(),
      chunks,
      loadTime: 0, // Would be measured from actual load events
      renderTime: 0, // Would be measured from render events
      interactiveTime: 0 // Would be measured from interactive events
    };
  }
}

// Resource preloading utilities
export const preloadResource = (href: string, as: string, crossorigin?: string) => {
  const link = document.createElement('link');
  link.rel = 'preload';
  link.href = href;
  link.as = as;
  if (crossorigin) link.crossOrigin = crossorigin;
  document.head.appendChild(link);
};

export const prefetchResource = (href: string) => {
  const link = document.createElement('link');
  link.rel = 'prefetch';
  link.href = href;
  document.head.appendChild(link);
};

// Critical resource hints
export const addCriticalResourceHints = () => {
  // Preconnect to external domains
  const domains = [
    'https://fonts.googleapis.com',
    'https://fonts.gstatic.com',
    'https://images.pexels.com'
  ];

  domains.forEach(domain => {
    const link = document.createElement('link');
    link.rel = 'preconnect';
    link.href = domain;
    link.crossOrigin = 'anonymous';
    document.head.appendChild(link);
  });
};

// Image optimization utilities
export const optimizeImage = (src: string, options: {
  width?: number;
  height?: number;
  quality?: number;
  format?: 'webp' | 'avif' | 'jpeg' | 'png';
} = {}) => {
  const { width, height, quality = 80, format = 'webp' } = options;
  
  // This would integrate with an image optimization service
  let optimizedSrc = src;
  
  if (width || height) {
    const params = new URLSearchParams();
    if (width) params.set('w', width.toString());
    if (height) params.set('h', height.toString());
    params.set('q', quality.toString());
    params.set('fm', format);
    
    optimizedSrc = `${src}?${params.toString()}`;
  }
  
  return optimizedSrc;
};

// Lazy loading intersection observer
export const createLazyLoadObserver = (callback: (entries: IntersectionObserverEntry[]) => void) => {
  const options = {
    root: null,
    rootMargin: '50px',
    threshold: 0.1
  };

  return new IntersectionObserver(callback, options);
};

// Memory usage monitoring
export const getMemoryUsage = () => {
  if ('memory' in performance) {
    const memory = (performance as any).memory;
    return {
      usedJSHeapSize: memory.usedJSHeapSize,
      totalJSHeapSize: memory.totalJSHeapSize,
      jsHeapSizeLimit: memory.jsHeapSizeLimit,
      usagePercentage: (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100
    };
  }
  return null;
};

// Network information
export const getNetworkInfo = () => {
  if ('connection' in navigator) {
    const connection = (navigator as any).connection;
    return {
      effectiveType: connection.effectiveType,
      downlink: connection.downlink,
      rtt: connection.rtt,
      saveData: connection.saveData
    };
  }
  return null;
};

// Adaptive loading based on network conditions
export const shouldLoadHighQuality = () => {
  const networkInfo = getNetworkInfo();
  if (!networkInfo) return true; // Default to high quality if no info available
  
  // Don't load high quality on slow connections or when data saver is on
  if (networkInfo.saveData || networkInfo.effectiveType === 'slow-2g' || networkInfo.effectiveType === '2g') {
    return false;
  }
  
  return true;
};

// Bundle splitting recommendations
export const getBundleSplittingRecommendations = (chunks: ChunkInfo[]) => {
  const recommendations: string[] = [];
  
  // Check for large chunks
  const largeChunks = chunks.filter(chunk => chunk.size > 250000); // 250KB
  if (largeChunks.length > 0) {
    recommendations.push(`Consider splitting large chunks: ${largeChunks.map(c => c.name).join(', ')}`);
  }
  
  // Check for unused chunks
  const unusedChunks = chunks.filter(chunk => chunk.priority === 'low');
  if (unusedChunks.length > 0) {
    recommendations.push(`Consider removing unused chunks: ${unusedChunks.map(c => c.name).join(', ')}`);
  }
  
  // Check for duplicate modules
  const allModules = chunks.flatMap(chunk => chunk.modules);
  const duplicateModules = allModules.filter((module, index) => allModules.indexOf(module) !== index);
  if (duplicateModules.length > 0) {
    recommendations.push(`Consider extracting common modules: ${[...new Set(duplicateModules)].join(', ')}`);
  }
  
  return recommendations;
};

// Performance budget checker
export const checkPerformanceBudget = (metrics: PerformanceMetrics, budget: Partial<PerformanceMetrics>) => {
  const violations: string[] = [];
  
  Object.entries(budget).forEach(([key, value]) => {
    const metricKey = key as keyof PerformanceMetrics;
    if (metrics[metricKey] > value!) {
      violations.push(`${key}: ${metrics[metricKey]}ms exceeds budget of ${value}ms`);
    }
  });
  
  return violations;
};

// Export singleton instances
export const performanceMonitor = new PerformanceMonitor();
export const bundleAnalyzer = BundleAnalyzer.getInstance();
