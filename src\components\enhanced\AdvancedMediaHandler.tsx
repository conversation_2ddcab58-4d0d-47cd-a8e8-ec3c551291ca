import React, { useState, useRef, useCallback, memo, useMemo } from 'react';
import { Upload, X, Play, Pause, Volume2, VolumeX, RotateCw, Crop, Filter, Download, Share2, Edit3, Maximize } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'sonner';

interface MediaFile {
  id: string;
  file: File;
  type: 'image' | 'video' | 'audio';
  url: string;
  thumbnail?: string;
  duration?: number;
  size: number;
  dimensions?: { width: number; height: number };
  metadata?: Record<string, any>;
}

interface MediaEditOptions {
  brightness: number;
  contrast: number;
  saturation: number;
  blur: number;
  rotation: number;
  crop: { x: number; y: number; width: number; height: number };
  filters: string[];
}

interface AdvancedMediaHandlerProps {
  onMediaSelect: (files: MediaFile[]) => void;
  maxFiles?: number;
  acceptedTypes?: string[];
  maxFileSize?: number; // in MB
  enableEditing?: boolean;
  enableBatchProcessing?: boolean;
  className?: string;
}

const AdvancedMediaHandler: React.FC<AdvancedMediaHandlerProps> = memo(({
  onMediaSelect,
  maxFiles = 10,
  acceptedTypes = ['image/*', 'video/*', 'audio/*'],
  maxFileSize = 100,
  enableEditing = true,
  enableBatchProcessing = true,
  className = ''
}) => {
  const [mediaFiles, setMediaFiles] = useState<MediaFile[]>([]);
  const [selectedFile, setSelectedFile] = useState<MediaFile | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editOptions, setEditOptions] = useState<MediaEditOptions>({
    brightness: 100,
    contrast: 100,
    saturation: 100,
    blur: 0,
    rotation: 0,
    crop: { x: 0, y: 0, width: 100, height: 100 },
    filters: []
  });
  const [isProcessing, setIsProcessing] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});
  
  const fileInputRef = useRef<HTMLInputElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  const availableFilters = useMemo(() => [
    { name: 'None', value: 'none' },
    { name: 'Sepia', value: 'sepia(100%)' },
    { name: 'Grayscale', value: 'grayscale(100%)' },
    { name: 'Vintage', value: 'sepia(50%) contrast(120%) brightness(110%)' },
    { name: 'Cool', value: 'hue-rotate(180deg) saturate(120%)' },
    { name: 'Warm', value: 'hue-rotate(30deg) saturate(110%)' },
    { name: 'High Contrast', value: 'contrast(150%) brightness(110%)' },
    { name: 'Soft', value: 'blur(1px) brightness(105%)' }
  ], []);

  const handleFileSelect = useCallback(async (files: FileList) => {
    const newFiles: MediaFile[] = [];
    
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      
      // Validate file size
      if (file.size > maxFileSize * 1024 * 1024) {
        toast.error(`File ${file.name} is too large. Max size: ${maxFileSize}MB`);
        continue;
      }
      
      // Validate file type
      const isValidType = acceptedTypes.some(type => {
        if (type.endsWith('/*')) {
          return file.type.startsWith(type.slice(0, -1));
        }
        return file.type === type;
      });
      
      if (!isValidType) {
        toast.error(`File type ${file.type} is not supported`);
        continue;
      }
      
      const url = URL.createObjectURL(file);
      const mediaFile: MediaFile = {
        id: `${Date.now()}-${i}`,
        file,
        type: file.type.startsWith('image/') ? 'image' : 
              file.type.startsWith('video/') ? 'video' : 'audio',
        url,
        size: file.size
      };
      
      // Get metadata for images and videos
      if (mediaFile.type === 'image') {
        const img = new Image();
        img.onload = () => {
          mediaFile.dimensions = { width: img.width, height: img.height };
          setMediaFiles(prev => prev.map(f => f.id === mediaFile.id ? mediaFile : f));
        };
        img.src = url;
      } else if (mediaFile.type === 'video') {
        const video = document.createElement('video');
        video.onloadedmetadata = () => {
          mediaFile.duration = video.duration;
          mediaFile.dimensions = { width: video.videoWidth, height: video.videoHeight };
          
          // Generate thumbnail
          const canvas = document.createElement('canvas');
          canvas.width = 200;
          canvas.height = 150;
          const ctx = canvas.getContext('2d');
          if (ctx) {
            video.currentTime = 1; // Seek to 1 second
            video.onseeked = () => {
              ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
              mediaFile.thumbnail = canvas.toDataURL();
              setMediaFiles(prev => prev.map(f => f.id === mediaFile.id ? mediaFile : f));
            };
          }
        };
        video.src = url;
      } else if (mediaFile.type === 'audio') {
        const audio = new Audio();
        audio.onloadedmetadata = () => {
          mediaFile.duration = audio.duration;
          setMediaFiles(prev => prev.map(f => f.id === mediaFile.id ? mediaFile : f));
        };
        audio.src = url;
      }
      
      newFiles.push(mediaFile);
    }
    
    setMediaFiles(prev => {
      const combined = [...prev, ...newFiles];
      if (combined.length > maxFiles) {
        toast.error(`Maximum ${maxFiles} files allowed`);
        return prev;
      }
      return combined;
    });
    
    // Simulate upload progress
    newFiles.forEach(file => {
      let progress = 0;
      const interval = setInterval(() => {
        progress += Math.random() * 20;
        if (progress >= 100) {
          progress = 100;
          clearInterval(interval);
          setUploadProgress(prev => {
            const { [file.id]: _, ...rest } = prev;
            return rest;
          });
        }
        setUploadProgress(prev => ({ ...prev, [file.id]: progress }));
      }, 200);
    });
  }, [maxFiles, maxFileSize, acceptedTypes]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files);
    }
  }, [handleFileSelect]);

  const handleRemoveFile = useCallback((fileId: string) => {
    setMediaFiles(prev => {
      const file = prev.find(f => f.id === fileId);
      if (file) {
        URL.revokeObjectURL(file.url);
      }
      return prev.filter(f => f.id !== fileId);
    });
    
    if (selectedFile?.id === fileId) {
      setSelectedFile(null);
      setIsEditing(false);
    }
  }, [selectedFile]);

  const handleEditFile = useCallback((file: MediaFile) => {
    setSelectedFile(file);
    setIsEditing(true);
    setEditOptions({
      brightness: 100,
      contrast: 100,
      saturation: 100,
      blur: 0,
      rotation: 0,
      crop: { x: 0, y: 0, width: 100, height: 100 },
      filters: []
    });
  }, []);

  const applyImageFilters = useCallback((file: MediaFile, options: MediaEditOptions) => {
    if (file.type !== 'image') return file.url;
    
    const canvas = canvasRef.current;
    if (!canvas) return file.url;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return file.url;
    
    const img = new Image();
    img.onload = () => {
      canvas.width = img.width;
      canvas.height = img.height;
      
      // Apply filters
      ctx.filter = `
        brightness(${options.brightness}%)
        contrast(${options.contrast}%)
        saturate(${options.saturation}%)
        blur(${options.blur}px)
        ${options.filters.join(' ')}
      `;
      
      // Apply rotation
      if (options.rotation !== 0) {
        ctx.save();
        ctx.translate(canvas.width / 2, canvas.height / 2);
        ctx.rotate((options.rotation * Math.PI) / 180);
        ctx.drawImage(img, -img.width / 2, -img.height / 2);
        ctx.restore();
      } else {
        ctx.drawImage(img, 0, 0);
      }
    };
    img.src = file.url;
    
    return canvas.toDataURL();
  }, []);

  const handleSaveEdit = useCallback(() => {
    if (!selectedFile) return;
    
    setIsProcessing(true);
    
    // Simulate processing time
    setTimeout(() => {
      const editedUrl = applyImageFilters(selectedFile, editOptions);
      
      setMediaFiles(prev => prev.map(file => 
        file.id === selectedFile.id 
          ? { ...file, url: editedUrl }
          : file
      ));
      
      setIsProcessing(false);
      setIsEditing(false);
      toast.success('Changes applied successfully');
    }, 1000);
  }, [selectedFile, editOptions, applyImageFilters]);

  const handleBatchProcess = useCallback(() => {
    if (mediaFiles.length === 0) return;
    
    setIsProcessing(true);
    toast.success('Batch processing started...');
    
    // Simulate batch processing
    setTimeout(() => {
      setIsProcessing(false);
      toast.success('Batch processing completed');
    }, 2000);
  }, [mediaFiles]);

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className={`w-full ${className}`}>
      <canvas ref={canvasRef} className="hidden" />
      
      {/* File Upload Area */}
      <div
        className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center hover:border-blue-400 transition-colors cursor-pointer"
        onDrop={handleDrop}
        onDragOver={(e) => e.preventDefault()}
        onClick={() => fileInputRef.current?.click()}
      >
        <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <p className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Drop files here or click to upload
        </p>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          Support for images, videos, and audio files up to {maxFileSize}MB
        </p>
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept={acceptedTypes.join(',')}
          onChange={(e) => e.target.files && handleFileSelect(e.target.files)}
          className="hidden"
        />
      </div>

      {/* Media Files Grid */}
      {mediaFiles.length > 0 && (
        <div className="mt-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Uploaded Files ({mediaFiles.length})
            </h3>
            {enableBatchProcessing && mediaFiles.length > 1 && (
              <Button
                onClick={handleBatchProcess}
                disabled={isProcessing}
                variant="outline"
                size="sm"
              >
                {isProcessing ? 'Processing...' : 'Batch Process'}
              </Button>
            )}
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            <AnimatePresence>
              {mediaFiles.map((file) => (
                <motion.div
                  key={file.id}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  className="relative group"
                >
                  <Card className="overflow-hidden">
                    <div className="aspect-square relative bg-gray-100 dark:bg-gray-800">
                      {file.type === 'image' && (
                        <img
                          src={file.url}
                          alt={file.file.name}
                          className="w-full h-full object-cover"
                        />
                      )}
                      
                      {file.type === 'video' && (
                        <div className="w-full h-full flex items-center justify-center">
                          {file.thumbnail ? (
                            <img
                              src={file.thumbnail}
                              alt={file.file.name}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <Play className="w-12 h-12 text-gray-400" />
                          )}
                          <div className="absolute inset-0 bg-black/20 flex items-center justify-center">
                            <Play className="w-8 h-8 text-white" />
                          </div>
                        </div>
                      )}
                      
                      {file.type === 'audio' && (
                        <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-purple-400 to-pink-400">
                          <Volume2 className="w-12 h-12 text-white" />
                        </div>
                      )}
                      
                      {/* Upload Progress */}
                      {uploadProgress[file.id] !== undefined && uploadProgress[file.id] < 100 && (
                        <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                          <div className="text-white text-center">
                            <div className="w-16 h-16 border-4 border-white/30 border-t-white rounded-full animate-spin mb-2"></div>
                            <p className="text-sm">{Math.round(uploadProgress[file.id])}%</p>
                          </div>
                        </div>
                      )}
                      
                      {/* File Actions */}
                      <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                        <div className="flex space-x-1">
                          {enableEditing && file.type === 'image' && (
                            <Button
                              onClick={() => handleEditFile(file)}
                              size="sm"
                              variant="secondary"
                              className="w-8 h-8 p-0"
                            >
                              <Edit3 className="w-3 h-3" />
                            </Button>
                          )}
                          <Button
                            onClick={() => handleRemoveFile(file.id)}
                            size="sm"
                            variant="destructive"
                            className="w-8 h-8 p-0"
                          >
                            <X className="w-3 h-3" />
                          </Button>
                        </div>
                      </div>
                      
                      {/* File Type Badge */}
                      <Badge
                        variant="secondary"
                        className="absolute bottom-2 left-2 text-xs"
                      >
                        {file.type.toUpperCase()}
                      </Badge>
                    </div>
                    
                    <CardContent className="p-3">
                      <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                        {file.file.name}
                      </p>
                      <div className="flex items-center justify-between mt-1">
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {formatFileSize(file.size)}
                        </span>
                        {file.duration && (
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            {formatDuration(file.duration)}
                          </span>
                        )}
                      </div>
                      {file.dimensions && (
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {file.dimensions.width} × {file.dimensions.height}
                        </span>
                      )}
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        </div>
      )}

      {/* Image Editor Modal */}
      {isEditing && selectedFile && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        >
          <div className="w-full max-w-4xl bg-white dark:bg-gray-900 rounded-lg overflow-hidden">
            <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Edit Image
              </h3>
              <Button
                onClick={() => setIsEditing(false)}
                variant="ghost"
                size="sm"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
            
            <div className="flex">
              {/* Image Preview */}
              <div className="flex-1 p-4 bg-gray-50 dark:bg-gray-800">
                <div className="aspect-square bg-white dark:bg-gray-900 rounded-lg overflow-hidden">
                  <img
                    src={selectedFile.url}
                    alt={selectedFile.file.name}
                    className="w-full h-full object-contain"
                    style={{
                      filter: `
                        brightness(${editOptions.brightness}%)
                        contrast(${editOptions.contrast}%)
                        saturate(${editOptions.saturation}%)
                        blur(${editOptions.blur}px)
                        ${editOptions.filters.join(' ')}
                      `,
                      transform: `rotate(${editOptions.rotation}deg)`
                    }}
                  />
                </div>
              </div>
              
              {/* Edit Controls */}
              <div className="w-80 p-4 border-l border-gray-200 dark:border-gray-700">
                <div className="space-y-6">
                  {/* Brightness */}
                  <div>
                    <Label>Brightness: {editOptions.brightness}%</Label>
                    <Slider
                      value={[editOptions.brightness]}
                      onValueChange={([value]) => 
                        setEditOptions(prev => ({ ...prev, brightness: value }))
                      }
                      min={0}
                      max={200}
                      step={1}
                      className="mt-2"
                    />
                  </div>
                  
                  {/* Contrast */}
                  <div>
                    <Label>Contrast: {editOptions.contrast}%</Label>
                    <Slider
                      value={[editOptions.contrast]}
                      onValueChange={([value]) => 
                        setEditOptions(prev => ({ ...prev, contrast: value }))
                      }
                      min={0}
                      max={200}
                      step={1}
                      className="mt-2"
                    />
                  </div>
                  
                  {/* Saturation */}
                  <div>
                    <Label>Saturation: {editOptions.saturation}%</Label>
                    <Slider
                      value={[editOptions.saturation]}
                      onValueChange={([value]) => 
                        setEditOptions(prev => ({ ...prev, saturation: value }))
                      }
                      min={0}
                      max={200}
                      step={1}
                      className="mt-2"
                    />
                  </div>
                  
                  {/* Blur */}
                  <div>
                    <Label>Blur: {editOptions.blur}px</Label>
                    <Slider
                      value={[editOptions.blur]}
                      onValueChange={([value]) => 
                        setEditOptions(prev => ({ ...prev, blur: value }))
                      }
                      min={0}
                      max={10}
                      step={0.1}
                      className="mt-2"
                    />
                  </div>
                  
                  {/* Rotation */}
                  <div>
                    <Label>Rotation: {editOptions.rotation}°</Label>
                    <Slider
                      value={[editOptions.rotation]}
                      onValueChange={([value]) => 
                        setEditOptions(prev => ({ ...prev, rotation: value }))
                      }
                      min={-180}
                      max={180}
                      step={1}
                      className="mt-2"
                    />
                  </div>
                  
                  {/* Filters */}
                  <div>
                    <Label>Filters</Label>
                    <div className="grid grid-cols-2 gap-2 mt-2">
                      {availableFilters.map((filter) => (
                        <Button
                          key={filter.name}
                          onClick={() => {
                            if (filter.value === 'none') {
                              setEditOptions(prev => ({ ...prev, filters: [] }));
                            } else {
                              setEditOptions(prev => ({ 
                                ...prev, 
                                filters: [filter.value] 
                              }));
                            }
                          }}
                          variant={
                            (filter.value === 'none' && editOptions.filters.length === 0) ||
                            editOptions.filters.includes(filter.value)
                              ? 'default' 
                              : 'outline'
                          }
                          size="sm"
                          className="text-xs"
                        >
                          {filter.name}
                        </Button>
                      ))}
                    </div>
                  </div>
                </div>
                
                {/* Action Buttons */}
                <div className="flex space-x-2 mt-6">
                  <Button
                    onClick={handleSaveEdit}
                    disabled={isProcessing}
                    className="flex-1"
                  >
                    {isProcessing ? 'Applying...' : 'Apply Changes'}
                  </Button>
                  <Button
                    onClick={() => setIsEditing(false)}
                    variant="outline"
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      )}
      
      {/* Submit Button */}
      {mediaFiles.length > 0 && (
        <div className="mt-6 flex justify-end">
          <Button
            onClick={() => onMediaSelect(mediaFiles)}
            className="bg-blue-500 hover:bg-blue-600"
          >
            Use Selected Files ({mediaFiles.length})
          </Button>
        </div>
      )}
    </div>
  );
});

AdvancedMediaHandler.displayName = 'AdvancedMediaHandler';

export default AdvancedMediaHandler;
